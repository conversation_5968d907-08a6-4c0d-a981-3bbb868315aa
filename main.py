""" Gaze Tracking Pipeline -- Default Approach
Version:        0.1.0
Last update:    2025.06.10
Researcher:     MAKSYM CHERNOZHUKOV
Description:    Gaze tracking pipeline for ICMS dataset.
Future work:
    - Freezing backbone
    - Pruning
    - Quantization: 8int
    - Knowledge distillation
"""
import os
import warnings

import torch
import torch.nn.functional as F

# Enable Tensor Core operations for better performance on supported GPUs
torch.set_float32_matmul_precision('high')

from pytorch_lightning import seed_everything, Trainer
from pytorch_lightning.loggers import TensorBoardLogger
from pytorch_lightning.utilities.types import STEP_OUTPUT
from pytorch_lightning.callbacks import ModelCheckpoint

from nets.nn2 import FinalModel
from utils.config import get_config
from utils.gaze_dataset import get_dataloaders
from utils.pytorch_lightning_logger import MetricsLogger
from utils.util import calculate_gaze_angle_error, PitchYaw, plot_prediction_vs_ground_truth, log_figure
from utils import util2


warnings.filterwarnings("ignore", category=DeprecationWarning)

util2.setup_logger()
util2.setup_multi_processes()
util2.init_deterministic_seed()


class Model(FinalModel):
    """
    PyTorch Lightning module for gaze tracking.

    This class extends the FinalModel neural network architecture with training,
    validation, and testing logic. It handles the forward pass, loss calculation,
    and metric logging.
    """
    def __init__(self,
                 learning_rate: float = 0.001,
                 weight_decay: float = 0.0,
                 loss_weights: dict = None,
                 *args, **kwargs):
        """
        Initialize the gaze tracking model.

        Args:
            learning_rate: Learning rate for the optimizer
            weight_decay: Weight decay (L2 regularization) for the optimizer
            loss_weights: Dictionary with weights for different loss components
                          (e.g., {'angular': 1.0, 'l1': 0.1})
            *args, **kwargs: Additional arguments passed to FinalModel
        """
        super().__init__(*args, **kwargs)
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay

        # Set default loss weights if not provided
        if loss_weights is None:
            self.loss_weights = {'angular': 1.0, 'l1': 0.1}
        else:
            self.loss_weights = loss_weights

        # Initialize lists to store step-wise metrics for averaging
        self._train_step_outputs = []
        self._val_step_outputs = []
        self._test_step_outputs = []

        # Initialize dictionary to store epoch-wise averages
        self._epoch_metrics = {
            'train': [],
            'val': [],
            'test': []
        }

        self.save_hyperparameters()

    def configure_optimizers(self):
        """
        Configure the optimizer and learning rate scheduler for stable training.

        Returns:
            Dictionary containing optimizer and optional scheduler configuration
        """
        optimizer = torch.optim.Adam(
            self.parameters(),
            lr=self.learning_rate,
            weight_decay=self.weight_decay,
            eps=1e-8,  # Prevent division by zero
            betas=(0.9, 0.999)  # Standard Adam parameters
        )

        # Optional: Add learning rate scheduler for better convergence
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.5,
            patience=10,
            verbose=True,
            min_lr=1e-7
        )

        return {
            "optimizer": optimizer,
            "lr_scheduler": {
                "scheduler": scheduler,
                "monitor": "val/loss",
                "frequency": 1
            }
        }
        
    def __step(self, batch: dict):
        """
        Perform a single step of the model (forward pass and loss calculation).

        Args:
            batch: Dictionary containing the input data

        Returns:
            tuple: (loss, labels, outputs)
        """
        # person_idx = batch['person_idx'].long()
        full_face_image = batch['full_face_image'].float()
        # right_eye_image = batch['right_eye_image'].float()
        # left_eye_image  = batch['left_eye_image'].float()

        gaze_pitch = batch['gaze_pitch'].float()
        gaze_yaw   = batch['gaze_yaw'].float()
        labels = torch.stack([gaze_pitch, gaze_yaw], dim=1)

        # outputs = self(person_idx, full_face_image, right_eye_image, left_eye_image)
        outputs = self(full_face_image)

        # Calculate losses with configurable weights and safety checks
        loss_ang = calculate_gaze_angle_error(labels, outputs)[0]

        # Use L1 loss instead of MSE for better stability
        loss_l1 = F.mse_loss(outputs, labels)

        # Combine losses with safety check
        loss = (self.loss_weights['angular'] * loss_ang) + (self.loss_weights['l1'] * loss_l1)

        return loss, labels, outputs

    def training_step(self, batch: dict, _batch_idx: int) -> STEP_OUTPUT:
        """
        Perform a single training step and store metrics for averaging.

        Args:
            batch: Dictionary containing the input data
            batch_idx: Index of the current batch

        Returns:
            Loss value for the batch
        """
        loss, labels, outputs = self.__step(batch)

        # Calculate angular error in degrees
        error_rad, error_deg = calculate_gaze_angle_error(labels, outputs)

        # Store step-wise metrics for epoch averaging
        self._train_step_outputs.append({
            'loss': loss.item(),
            'error_rad': error_rad.item(),
            'error_deg': error_deg.item()
        })

        # Log individual step metrics
        self.log('train/loss', loss, on_step=False, on_epoch=True)
        self.log('train/angular_error', error_deg, on_step=False, on_epoch=True)

        return loss

    def validation_step(self, batch: dict, _batch_idx: int) -> STEP_OUTPUT:
        """
        Perform a single validation step and store metrics for averaging.

        Args:
            batch: Dictionary containing the input data
            batch_idx: Index of the current batch

        Returns:
            Loss value for the batch
        """
        loss, labels, outputs = self.__step(batch)

        # Calculate angular error in degrees
        error_rad, error_deg = calculate_gaze_angle_error(labels, outputs)

        # Store step-wise metrics for epoch averaging
        self._val_step_outputs.append({
            'loss': loss.item(),
            'error_rad': error_rad.item(),
            'error_deg': error_deg.item()
        })

        # Log individual step metrics
        self.log('val/loss', loss, on_step=False, on_epoch=True)
        self.log('val/angular_error', error_deg, on_step=False, on_epoch=True)
        return loss

    def test_step(self, batch: dict, _batch_idx: int) -> STEP_OUTPUT:
        """
        Perform a single test step and store metrics for averaging.

        Args:
            batch: Dictionary containing the input data
            batch_idx: Index of the current batch

        Returns:
            Loss value for the batch
        """
        loss, labels, outputs = self.__step(batch)

        # Calculate angular error in degrees
        error_rad, error_deg = calculate_gaze_angle_error(labels, outputs)

        # Store step-wise metrics for epoch averaging
        self._test_step_outputs.append({
            'loss': loss.item(),
            'error_rad': error_rad.item(),
            'error_deg': error_deg.item()
        })

        # Log individual step metrics
        self.log('test/loss', loss, on_step=False, on_epoch=True)
        self.log('test/angular_error', error_deg, on_step=False, on_epoch=True)
        return loss

    def on_train_epoch_end(self) -> None:
        """Calculate and store average training metrics for the epoch."""
        if self._train_step_outputs:
            avg_loss = sum(x['loss'] for x in self._train_step_outputs) / len(self._train_step_outputs)
            avg_error_rad = sum(x['error_rad'] for x in self._train_step_outputs) / len(self._train_step_outputs)
            avg_error_deg = sum(x['error_deg'] for x in self._train_step_outputs) / len(self._train_step_outputs)

            # Store epoch averages
            epoch_metrics = {
                'epoch': self.current_epoch,
                'avg_loss': avg_loss,
                'avg_error_rad': avg_error_rad,
                'avg_error_deg': avg_error_deg,
                'num_batches': len(self._train_step_outputs)
            }
            self._epoch_metrics['train'].append(epoch_metrics)

        # Clear step outputs for next epoch
        self._train_step_outputs.clear()

    def on_validation_epoch_end(self) -> None:
        """Calculate and store average validation metrics for the epoch."""
        if self._val_step_outputs:
            avg_loss = sum(x['loss'] for x in self._val_step_outputs) / len(self._val_step_outputs)
            avg_error_rad = sum(x['error_rad'] for x in self._val_step_outputs) / len(self._val_step_outputs)
            avg_error_deg = sum(x['error_deg'] for x in self._val_step_outputs) / len(self._val_step_outputs)

            # Store epoch averages
            epoch_metrics = {
                'epoch': self.current_epoch,
                'avg_loss': avg_loss,
                'avg_error_rad': avg_error_rad,
                'avg_error_deg': avg_error_deg,
                'num_batches': len(self._val_step_outputs)
            }
            self._epoch_metrics['val'].append(epoch_metrics)

        # print()
        # print()
        # print(len(self._epoch_metrics['train']))
        # print(len(self._epoch_metrics['val']))
        # print()
        # print()
        
        # Clear step outputs for next epoch
        self._val_step_outputs.clear()

    def on_test_epoch_end(self) -> None:
        """Calculate and store average test metrics for the epoch."""
        if self._test_step_outputs:
            avg_loss = sum(x['loss'] for x in self._test_step_outputs) / len(self._test_step_outputs)
            avg_error_rad = sum(x['error_rad'] for x in self._test_step_outputs) / len(self._test_step_outputs)
            avg_error_deg = sum(x['error_deg'] for x in self._test_step_outputs) / len(self._test_step_outputs)

            # Store epoch averages
            epoch_metrics = {
                'epoch': self.current_epoch,
                'avg_loss': avg_loss,
                'avg_error_rad': avg_error_rad,
                'avg_error_deg': avg_error_deg,
                'num_batches': len(self._test_step_outputs)
            }
            self._epoch_metrics['test'].append(epoch_metrics)

        # Clear step outputs for next epoch
        self._test_step_outputs.clear()

    # def save_epoch_metrics(self, save_path: str = None) -> None:
    #     """
    #     Save epoch-wise average metrics to CSV files.

    #     Args:
    #         save_path: Directory to save CSV files. If None, uses logger's log_dir
    #     """
    #     import pandas as pd
    #     import os

    #     if save_path is None:
    #         save_path = self.logger.log_dir if self.logger else "."

    #     # Save training metrics
    #     if self._epoch_metrics['train']:
    #         train_df = pd.DataFrame(self._epoch_metrics['train'])
    #         train_path = os.path.join(save_path, 'train_epoch_metrics.csv')
    #         train_df.to_csv(train_path, index=False)
    #         print(f"Training epoch metrics saved to: {train_path}")

    #     # Save validation metrics
    #     if self._epoch_metrics['val']:
    #         val_df = pd.DataFrame(self._epoch_metrics['val'])
    #         val_path = os.path.join(save_path, 'val_epoch_metrics.csv')
    #         val_df.to_csv(val_path, index=False)
    #         print(f"Validation epoch metrics saved to: {val_path}")

    #     # Save test metrics
    #     if self._epoch_metrics['test']:
    #         test_df = pd.DataFrame(self._epoch_metrics['test'])
    #         test_path = os.path.join(save_path, 'test_epoch_metrics.csv')
    #         test_df.to_csv(test_path, index=False)
    #         print(f"Test epoch metrics saved to: {test_path}")

    # def get_epoch_metrics(self, phase: str = 'all') -> dict:
    #     """
    #     Get stored epoch-wise average metrics.

    #     Args:
    #         phase: Which phase metrics to return ('train', 'val', 'test', or 'all')

    #     Returns:
    #         Dictionary containing epoch metrics for the specified phase(s)
    #     """
    #     if phase == 'all':
    #         return self._epoch_metrics.copy()
    #     elif phase in self._epoch_metrics:
    #         return {phase: self._epoch_metrics[phase].copy()}
    #     else:
    #         raise ValueError(f"Invalid phase '{phase}'. Must be 'train', 'val', 'test', or 'all'")

    # def print_epoch_summary(self) -> None:
    #     """Print a summary of the latest epoch metrics for all phases."""
    #     print("\n" + "="*60)
    #     print(f"EPOCH {self.current_epoch} SUMMARY")
    #     print("="*60)

    #     for phase in ['train', 'val', 'test']:
    #         if self._epoch_metrics[phase]:
    #             latest = self._epoch_metrics[phase][-1]
    #             print(f"{phase.upper():>5}: Loss={latest['avg_loss']:.4f}, "
    #                   f"Error={latest['avg_error_deg']:.2f}°, "
    #                   f"Batches={latest['num_batches']}")
    #     print("="*60)


def main(config):
    """
    Main function to train and evaluate the gaze tracking model.

    Args:
        config: Configuration dictionary containing all parameters for the pipeline
    """
    # Training parameters
    train = config['training']['train']
    test = config['training']['test']
    epochs = config['training']['epochs']
    batch_size = config['training']['batch_size']
    val_split = config['training']['val_split']
    learning_rate = config['training']['learning_rate']
    weight_decay = config['training']['weight_decay']
    gpus = config['training']['gpus']
    print_interval = config['training']['print_interval']
    dataset_path = config['training']['dataset_path']
    checkpoint_path = config['training']['checkpoint_path']
    save_dir = config['training']['save_dir']
    loss_weights = config['training'].get('loss_weights', {'angular': 1.0, 'l1': 0.1})
    seed = config['training'].get('seed', None)

    # Augmentation parameters
    augmentation_config = config.get('augmentation', {
        'enabled': True,
        'shift_limit': 0.2,
        'scale_limit': 0.1,
        'rotate_limit': 10,
        'brightness_contrast': False,
        'blur': False
    })

    # Logging parameters
    logging_config = config.get('logging', {
        'tensorboard': True,
        'log_images': True,
        'log_frequency': 1,
        'save_model_frequency': 5
    })

    # Set random seed for reproducibility
    if seed is not None:
        seed_everything(seed)

    # Load model with configurable parameters
    if checkpoint_path and os.path.isfile(checkpoint_path):
        print(f"Loading pretrained model from '{checkpoint_path}'")
        model = Model.load_from_checkpoint(
            checkpoint_path,
            learning_rate=learning_rate,
            weight_decay=weight_decay,
            loss_weights=loss_weights
        )
    else:
        print("No pretrained model found or path not provided. Training from 'scratch'.")
        model = Model(
            learning_rate=learning_rate,
            weight_decay=weight_decay,
            loss_weights=loss_weights
        )

    # Load dataloaders with configurable augmentation
    train_loader, val_loader, test_loader = get_dataloaders(
        data_root=dataset_path,
        batch_size=batch_size,
        val_split=val_split,
        augmentation_config=augmentation_config
    )

    # Set up callbacks and logger based on config
    tb_logger = TensorBoardLogger(
        save_dir=save_dir,
        name=None
    )

    # Create callbacks list
    callbacks = []

    # Add metrics logger callback
    metrics_cb = MetricsLogger(
        test_loader=None,
        freq_epochs=print_interval
    )
    callbacks.append(metrics_cb)

    # Add model checkpoint callback
    best_ckpt_cb = ModelCheckpoint(
        monitor='val/angular_error',
        mode='min',
        save_top_k=1,
        filename='best',
        auto_insert_metric_name=True,
        save_last=True
    )
    callbacks.append(best_ckpt_cb)

    # Set up Trainer with configurable parameters and stability improvements
    trainer = Trainer(
        accelerator='auto',             # automatically use GPU if available
        devices=gpus,
        max_epochs=epochs,
        default_root_dir='./saved_models/',
        logger=tb_logger,
        log_every_n_steps=logging_config.get('log_frequency', 1),
        callbacks=callbacks,
        benchmark=False,
        deterministic=True,             # for reproducibility
        gradient_clip_val=1.0,          # Clip gradients to prevent explosion
        gradient_clip_algorithm='norm', # Use gradient norm clipping
        precision='32-true',            # Use full precision to avoid numerical issues
    )

    # Train and test the model
    if train:
        trainer.fit(model, train_loader, val_loader)
        trainer.test(model, test_loader, ckpt_path='best')
    if test:
        trainer.test(model, test_loader)


if __name__ == '__main__':
    # Load configuration from file and update with command-line arguments
    config = get_config()

    # Run the main function with the configuration
    main(config)
    
""" Resutls
3.9025521278381348    face+eyes
3.7355329990386963    face        runs/version_138/checkpoints/best.ckpt
5.741062164306641     face256
"""
