import os

import torch
import torch.nn.functional as F

# Enable Tensor Core operations for better performance on supported GPUs
torch.set_float32_matmul_precision('high')

from pytorch_lightning import seed_everything, Trainer
from pytorch_lightning.loggers import TensorBoardLogger
from pytorch_lightning.utilities.types import STEP_OUTPUT
from pytorch_lightning.callbacks import ModelCheckpoint

from nets.nn import FinalModel
from utils.config import get_config
from utils.gaze_dataset import get_dataloaders
from utils.pytorch_lightning_logger import MetricsLogger
from utils.util import calc_angle_error, PitchYaw, plot_prediction_vs_ground_truth, log_figure, angular_loss


class Model(FinalModel):
    """
    PyTorch Lightning module for gaze tracking.

    This class extends the FinalModel neural network architecture with training,
    validation, and testing logic. It handles the forward pass, loss calculation,
    and metric logging.
    """
    def __init__(self,
                 learning_rate: float = 0.001,
                 weight_decay: float = 0.0,
                 loss_weights: dict = None,
                 *args, **kwargs):
        """
        Initialize the gaze tracking model.

        Args:
            learning_rate: Learning rate for the optimizer
            weight_decay: Weight decay (L2 regularization) for the optimizer
            loss_weights: Dictionary with weights for different loss components
                          (e.g., {'angular': 1.0, 'l1': 0.1})
            *args, **kwargs: Additional arguments passed to FinalModel
        """
        super().__init__(*args, **kwargs)
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay

        # Set default loss weights if not provided
        if loss_weights is None:
            self.loss_weights = {'angular': 1.0, 'l1': 0.1}
        else:
            self.loss_weights = loss_weights

        self.save_hyperparameters()

    def configure_optimizers(self):
        """Configure the optimizer for training."""
        return torch.optim.Adam(self.parameters(), lr=self.learning_rate, weight_decay=self.weight_decay)

    def __step(self, batch: dict):
        """
        Perform a single step of the model (forward pass and loss calculation).

        Args:
            batch: Dictionary containing the input data

        Returns:
            tuple: (loss, labels, outputs)
        """
        person_idx = batch['person_idx'].long()
        full_face_image = batch['full_face_image'].float()
        right_eye_image = batch['right_eye_image'].float()
        left_eye_image  = batch['left_eye_image'].float()

        gaze_pitch = batch['gaze_pitch'].float()
        gaze_yaw   = batch['gaze_yaw'].float()
        labels = torch.stack([gaze_pitch, gaze_yaw], dim=1)

        outputs = self(person_idx, full_face_image, right_eye_image, left_eye_image)

        # Calculate losses with configurable weights
        loss_ang = angular_loss(labels, outputs)
        loss_l1 = F.l1_loss(outputs, labels)    # F.mse_loss(outputs, labels)
        loss = (self.loss_weights['angular'] * loss_ang) + (self.loss_weights['l1'] * loss_l1)

        return loss, labels, outputs

    def training_step(self, batch: dict, _batch_idx: int) -> STEP_OUTPUT:
        """
        Perform a single training step.

        Args:
            batch: Dictionary containing the input data
            _batch_idx: Index of the current batch (not used)

        Returns:
            Loss value for the batch
        """
        loss, labels, outputs = self.__step(batch)
        self.log('train/loss', loss)
        self.log('train/angular_error', calc_angle_error(labels, outputs))
        return loss

    def on_validation_epoch_start(self) -> None:
        """Initialize list to store validation outputs for the epoch."""
        self._val_outputs = []

    def validation_step(self, batch: dict, _batch_idx: int) -> STEP_OUTPUT:
        """
        Perform a single validation step.

        Args:
            batch: Dictionary containing the input data
            _batch_idx: Index of the current batch (not used)

        Returns:
            Loss value for the batch
        """
        loss, labels, outputs = self.__step(batch)
        self.log('val/loss', loss)
        self.log('val/angular_error', calc_angle_error(labels, outputs))
        self._val_outputs.append({'labels': labels, 'outputs': outputs})
        return loss

    def on_validation_epoch_end(self) -> None:
        """
        Generate and log validation visualizations at the end of the epoch.
        Creates scatter plots of predicted vs. ground truth values for pitch and yaw.
        """
        labels = torch.cat([o['labels'] for o in self._val_outputs], dim=0)
        preds  = torch.cat([o['outputs'] for o in self._val_outputs], dim=0)
        fig_p = plot_prediction_vs_ground_truth(labels, preds, PitchYaw.PITCH)
        fig_y = plot_prediction_vs_ground_truth(labels, preds, PitchYaw.YAW)
        log_figure(self.logger, 'val/pitch', fig_p, self.global_step)
        log_figure(self.logger, 'val/yaw', fig_y, self.global_step)

    def on_test_epoch_start(self) -> None:
        """Initialize list to store test outputs for the epoch."""
        self._test_outputs = []

    def test_step(self, batch: dict, _batch_idx: int) -> STEP_OUTPUT:
        """
        Perform a single test step.

        Args:
            batch: Dictionary containing the input data
            _batch_idx: Index of the current batch (not used)

        Returns:
            Loss value for the batch
        """
        loss, labels, outputs = self.__step(batch)
        self.log('test/loss', loss)
        self.log('test/angular_error', calc_angle_error(labels, outputs))
        self._test_outputs.append({'labels': labels, 'outputs': outputs})
        return loss

    def on_test_epoch_end(self) -> None:
        """
        Generate and log test visualizations at the end of the epoch.
        Creates scatter plots of predicted vs. ground truth values for pitch and yaw.
        """
        labels = torch.cat([o['labels'] for o in self._test_outputs], dim=0)
        preds  = torch.cat([o['outputs'] for o in self._test_outputs], dim=0)
        fig_p = plot_prediction_vs_ground_truth(labels, preds, PitchYaw.PITCH)
        fig_y = plot_prediction_vs_ground_truth(labels, preds, PitchYaw.YAW)
        log_figure(self.logger, 'test/pitch', fig_p, self.global_step)
        log_figure(self.logger, 'test/yaw', fig_y, self.global_step)


def main(config):
    """
    Main function to train and evaluate the gaze tracking model.

    Args:
        config: Configuration dictionary containing all parameters for the pipeline
    """
    # Training parameters
    train = config['training']['train']
    test = config['training']['test']
    epochs = config['training']['epochs']
    batch_size = config['training']['batch_size']
    val_split = config['training']['val_split']
    learning_rate = config['training']['learning_rate']
    weight_decay = config['training']['weight_decay']
    gpus = config['training']['gpus']
    print_interval = config['training']['print_interval']
    dataset_path = config['training']['dataset_path']
    checkpoint_path = config['training']['checkpoint_path']
    save_dir = config['training']['save_dir']
    loss_weights = config['training'].get('loss_weights', {'angular': 1.0, 'l1': 0.1})
    seed = config['training'].get('seed', None)
        
    # Augmentation parameters
    augmentation_config = config.get('augmentation', {
        'enabled': True,
        'shift_limit': 0.2,
        'scale_limit': 0.1,
        'rotate_limit': 10,
        'brightness_contrast': False,
        'blur': False
    })

    # Logging parameters
    logging_config = config.get('logging', {
        'tensorboard': True,
        'log_images': True,
        'log_frequency': 1,
        'save_model_frequency': 5
    })

    # Set random seed for reproducibility
    if seed is not None:
        seed_everything(seed)
        
    # Load model with configurable parameters
    if checkpoint_path and os.path.isfile(checkpoint_path):
        print(f"Loading pretrained model from '{checkpoint_path}'")
        model = Model.load_from_checkpoint(
            checkpoint_path,
            learning_rate=learning_rate,
            weight_decay=weight_decay,
            loss_weights=loss_weights
        )
    else:
        print("No pretrained model found or path not provided. Training from 'scratch'.")
        model = Model(
            learning_rate=learning_rate,
            weight_decay=weight_decay,
            loss_weights=loss_weights
        )

    # Load dataloaders with configurable augmentation
    train_loader, val_loader, test_loader = get_dataloaders(
        data_root=dataset_path,
        batch_size=batch_size,
        val_split=val_split,
        augmentation_config=augmentation_config
    )

    # Set up callbacks and logger based on config
    tb_logger = TensorBoardLogger(
        save_dir=save_dir,
        name=None
    )

    # Create callbacks list
    callbacks = []

    # Add metrics logger callback
    metrics_cb = MetricsLogger(
        test_loader=None,
        freq_epochs=print_interval
    )
    callbacks.append(metrics_cb)

    # Add model checkpoint callback
    best_ckpt_cb = ModelCheckpoint(
        monitor='val/angular_error',
        mode='min',
        save_top_k=1,
        filename='best',
        auto_insert_metric_name=True,
        save_last=True
    )
    callbacks.append(best_ckpt_cb)

    # Set up Trainer with configurable parameters
    trainer = Trainer(
        accelerator='auto',  # automatically use GPU if available
        devices=gpus,
        max_epochs=epochs,
        default_root_dir='./saved_models/',
        logger=tb_logger,
        log_every_n_steps=logging_config.get('log_frequency', 1),
        callbacks=callbacks,
        benchmark=False,
        deterministic=True,  # for reproducibility
    )

    # Train and test the model
    if train:
        trainer.fit(model, train_loader, val_loader)
        trainer.test(model, test_loader, ckpt_path='best')

    if test:
        trainer.test(model, test_loader)


if __name__ == '__main__':
    # Load configuration from file and update with command-line arguments
    config = get_config()

    # Run the main function with the configuration
    main(config)
