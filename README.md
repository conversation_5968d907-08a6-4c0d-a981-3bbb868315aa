# Gaze Tracking Pipeline

A deep learning-based gaze tracking system that predicts a person's gaze direction (`pitch` and `yaw` angles) from face and eye images. This project uses a multi-input neural network architecture with subject-specific bias terms to achieve accurate gaze estimation.

## Key Features

- **Multi-input Neural Network**: Processes face and eye images simultaneously using separate CNN branches
- **Advanced Architecture**: VGG16-based backbone with dilated convolutions and Squeeze-and-Excitation attention
- **Subject-specific Calibration**: Support for personalized bias terms (currently disabled)
- **Comprehensive Data Pipeline**: Tools for preprocessing, normalization, and augmentation
- **Visualization Tools**: Generate gaze direction visualizations with ground truth vs. predictions
- **PyTorch Lightning**: Clean, modular training pipeline with automatic logging
- **Configurable Training**: YAML-based configuration system for all parameters
- **Reproducible Results**: Deterministic training with fixed random seeds

## Project Structure

```
├── configs/                        # Configuration files
├── datasets/                       # Directory for normalized datasets
├── nets/
│   └── nn.py                       # Neural network architecture
├── runs/                           # Training logs and checkpoints
├── scripts/
│   └── data_preprocessing/         # Data preprocessing scripts
│       ├── visualize_gaze_directions.py    # Gaze direction visualization
│       ├── gaze_dataset_utils.py           # Utilities for data preprocessing
│       ├── gaze_dataset_preprocessing.py   # Main preprocessing script
│       └── show_data_distribution.py       # Data distribution analysis
├── utils/
│   ├── config.py                   # Configuration utilities
│   ├── gaze_dataset.py             # Dataset loading and processing
│   ├── pytorch_lightning_logger.py # Training metrics logging
│   └── util.py                     # Utility functions
├── weights/                        # Pre-trained model weights
├── main.py                         # Main training script
└── README.md                       # Project documentation
```

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/gaze-tracking-pipeline.git
cd gaze-tracking-pipeline
```

2. Create a conda environment:
```bash
conda env create -f configs/requirements.yml
```

## Data Preparation

### Data Structure

The pipeline expects normalized data in the following structure:
```
datasets/DeltaX/dataset_normalized/
├── train/
│   ├── labels.csv
│   ├── p00/
│   │   ├── sample1-face.jpg
│   │   ├── sample1-left-eye.jpg
│   │   ├── sample1-right-eye.jpg
│   │   └── ...
│   ├── p01/
│   └── ...
└── test/
    ├── labels.csv
    ├── p00/
    │   ├── sample1-face.jpg
    │   ├── sample1-left-eye.jpg
    │   ├── sample1-right-eye.jpg
    │   └── ...
    ├── p01/
    └── ...
```

The `labels.csv` file should contain the following columns:
- `face_file_name`: Path to the face image relative to the data directory
- `left_eye`: Path to the left eye image
- `right_eye`: Path to the right eye image
- `pitch`: Gaze pitch angle in radians
- `yaw`: Gaze yaw angle in radians

### Data Preprocessing

To preprocess raw data, use the preprocessing script:

The script requires:
- `data.csv` file with gaze labels and metadata
- Raw images in `.jpg` format
- Depth data in `.npy` format
- Face landmarks in `.npy` format
- Camera calibration parameters in a YAML file

```bash
python scripts/data_preprocessing/gaze_dataset_preprocessing.py \
    --raw_base=path/to/raw_data \
    --normalized_base=path/to/output \
    --intrinsics_path=path/to/calibration.yaml
```

### Coordinate System Validation

The preprocessing pipeline includes robust validation of coordinate systems to ensure accurate gaze normalization. The system checks:

1. **Orthogonality**: Ensures all axes are perpendicular to each other
2. **Unit Vectors**: Verifies all axes have unit length
3. **Right-Hand Rule**: Confirms the coordinate system follows the right-hand rule

You can also configure preprocessing parameters in the `preprocessing` section of the configuration file:

```yaml
preprocessing:
  normalize_faces: true           # Whether to normalize face images
  face_detection_confidence: 0.5  # Confidence threshold for face detection
  eye_crop_width_ratio: 0.35      # Width of eye crop as ratio of face width
  eye_crop_height_ratio: 0.25     # Height of eye crop as ratio of face height
```

## Configuration

The pipeline uses a comprehensive YAML configuration system to control all aspects of training, evaluation, and data processing. The main configuration file is located at `configs/config.yaml`.

### Configuration Structure

The configuration file is organized into logical sections that control different aspects of the pipeline. All parameters are fully integrated and actively used throughout the codebase:

```yaml
# Random seed for reproducibility
seed: 42

# Data paths
data:
  dataset_path: "datasets/DeltaX/dataset_normalized"  # Root directory for normalized data
  dataset_path_raw: "datasets/DeltaX/dataset_raw"     # Root directory for raw data
  intrinsics_path: "configs/depth_camera_calibration.yaml" # Camera calibration file

# Training parameters
training:
  train: false               # Whether to train the model
  test: false                # Whether to test the model
  epochs: 100                # Number of training epochs
  batch_size: 16             # Batch size for training
  val_split: 0.1             # Validation split ratio
  learning_rate: 0.0001      # Learning rate for optimizer
  weight_decay: 0.0          # L2 regularization coefficient
  gpus: 1                    # Number of GPUs to use
  print_interval: 5          # Interval for printing metrics
  save_dir: "runs"           # Directory to save logs and checkpoints
  loss_weights:              # Weights for different loss components
    angular: 1.0             # Weight for angular loss
    l1: 0.1                  # Weight for L1 loss
  seed: 42                   # Random seed for reproducibility
  checkpoint_path: weights/author_subject-dependent.ckpt  # Path to pretrained model

# Model architecture parameters
model:
  face_cnn:
    pretrained: true         # Whether to use pretrained weights
    freeze_base: false       # Whether to freeze base layers
    input_size: [96, 96]     # Input size for face images [height, width]
  eye_cnn:
    pretrained: true         # Whether to use pretrained weights
    freeze_base: false       # Whether to freeze base layers
    input_size: [64, 96]     # Input size for eye images [height, width]
  fc:
    dropout_rate: 0.5        # Dropout rate for FC layers
    hidden_dims: [256, 64]   # Hidden dimensions for FC layers

# Data augmentation parameters
augmentation:
  enabled: true              # Whether to use data augmentation
  shift_limit: 0.2           # Maximum shift as a fraction of image size
  scale_limit: 0.1           # Maximum scale factor
  rotate_limit: 10           # Maximum rotation angle in degrees
  brightness_contrast: false # Whether to use brightness/contrast augmentation
  blur: false                # Whether to use blur augmentation

# Logging parameters
logging:
  tensorboard: true          # Whether to use TensorBoard
  log_images: true           # Whether to log images to TensorBoard
  log_frequency: 1           # Frequency of logging (in steps)
  save_model_frequency: 5    # Frequency of saving model checkpoints (in epochs)
```

You can modify any of these parameters to customize the training process. The pipeline now uses all these parameters to configure various aspects of the training process, including:

- **Loss Function**: The weights for angular and L1 loss components
- **Data Augmentation**: Various augmentation techniques with configurable parameters
- **Logging**: Frequency and content of TensorBoard logs
- **Model Architecture**: Input sizes, dropout rates, and layer configurations
- **Training Process**: Batch sizes, learning rates, and optimization parameters

## Training

> **Reproducibility Note**: The pipeline is configured for deterministic behavior to ensure consistent results across multiple runs. This is achieved by setting a fixed random seed (default: 42), using deterministic CUDA operations, and ensuring consistent data shuffling.

> **Performance Note**: The pipeline automatically enables Tensor Core operations on supported NVIDIA GPUs by setting `torch.set_float32_matmul_precision('high')`. This improves training performance with a minimal impact on precision.

To train the model using the default configuration:

```bash
python main.py --train
```

Or using command-line overrides:

```bash
python main.py \
    --train \
    --config=configs/config.yaml \
    --dataset_path=datasets/DeltaX/dataset_normalized \
    --checkpoint_path=weights/author_subject-dependent.ckpt
```

This will load all parameters from `configs/config.yaml` and start training.

### Command-line Arguments

Command-line arguments override values in the configuration file:

| Argument            | Description                   | Default                            |
| ------------------- | ----------------------------- | ---------------------------------- |
| `--config`          | Path to configuration file    | configs/config.yaml       |
| `--train`           | Train the model               | False                              |
| `--test`            | Test the model                | False                              |
| `--epochs`          | Number of training epochs     | 100                                |
| `--batch_size`      | Batch size                    | 16                                 |
| `--val_split`       | Validation split ratio        | 0.1                                |
| `--learning_rate`   | Learning rate                 | 0.001                              |
| `--weight_decay`    | L2 regularization coefficient | 0.0                                |
| `--gpus`            | Number of GPUs to use         | 1                                  |
| `--print_interval`  | Interval for printing metrics | 5                                  |
| `--dataset_path`    | Path to normalized data       | datasets/DeltaX/dataset_normalized |
| `--checkpoint_path` | Path to pretrained model      | None                               |

## Evaluation

To evaluate a trained model using command-line arguments:

```bash
python main.py --test \
    --checkpoint_path=runs/version_X/checkpoints/best.ckpt
```

## Visualize predictions

The visualization tool displays both ground truth and predicted gaze directions:

- **Green arrows**: Ground truth gaze direction
- **Red arrows**: Model's predicted gaze direction

The tool also calculates and displays the angular error between the two directions.

```bash
python scripts/data_preprocessing/visualize_gaze_directions.py
```

The visualization parameters are configured in `configs/config.yaml` under the `gaze_directions_visualization` section:

```yaml
gaze_directions_visualization:
  dataset_path: "datasets/DeltaX/dataset_normalized"
  output_path: "outputs/gaze_visualization_results"
  model_ckpt_path: "runs/version_X/checkpoints/best.ckpt"
  arrow_length: 50
  arrow_thickness: 2
```

## Model Architecture

The model architecture consists of:

1. **Face Processing Branch**:
   - VGG16-based CNN (first 4 convolutional layers, pretrained on ImageNet)
   - Additional convolutional layers with dilated convolutions
   - Fully connected layers for feature extraction

2. **Eye Processing Branch**:
   - Separate VGG16-based CNN for eye images
   - Squeeze-and-Excitation layers for attention
   - Feature fusion of both eyes

3. **Combined Processing**:
   - Concatenation of face and eye features
   - Fully connected layers for final prediction
   - Subject-specific bias terms for personalization

## Performance Metrics

The model is evaluated using:

- **Angular Error**: The angular difference between predicted and ground truth `gaze vectors`
- **L1 Loss**: The mean absolute difference between predicted and ground truth `pitch/yaw values`

## Visualization

During training and evaluation, the following visualizations are generated:

- Scatter plots of predicted vs. ground truth `pitch` and `yaw` angles
- Training and validation loss curves
- Angular error metrics
- Computational graph of the model architecture

These visualizations are saved to TensorBoard logs in the `runs/` directory. You can view them by running:

```bash
tensorboard --logdir=runs
```

## Code Quality and Best Practices

This codebase follows modern Python development practices:

### Documentation
- **Comprehensive docstrings**: All functions and classes have detailed docstrings with type hints
- **Clear variable naming**: Descriptive names following Python conventions
- **Inline comments**: Complex logic is explained with comments

### Code Organization
- **Modular design**: Separate modules for different functionalities (models, datasets, utilities)
- **Configuration-driven**: All parameters controlled through YAML configuration files
- **Type hints**: Full type annotation for better code clarity and IDE support

### Reproducibility
- **Fixed random seeds**: Deterministic behavior across runs
- **Version control**: All dependencies specified in requirements files
- **Logging**: Comprehensive logging of training metrics and hyperparameters

### Performance Optimizations
- **Tensor Core support**: Automatic use of mixed precision on supported GPUs
- **Efficient data loading**: Multi-worker data loading with proper transforms
- **Memory optimization**: Gradient accumulation and efficient batch processing
