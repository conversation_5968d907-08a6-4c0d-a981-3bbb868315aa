name: gaze-training-pipeline
channels:
  - pytorch
  - nvidia
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - blas=1.0=mkl
  - brotli-python=1.0.9=py39h6a678d5_9
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2025.2.25=h06a4308_0
  - certifi=2025.4.26=py39h06a4308_0
  - charset-normalizer=3.3.2=pyhd3eb1b0_0
  - cuda-cudart=12.1.105=0
  - cuda-cupti=12.1.105=0
  - cuda-libraries=12.1.0=0
  - cuda-nvrtc=12.1.105=0
  - cuda-nvtx=12.1.105=0
  - cuda-opencl=12.9.19=0
  - cuda-runtime=12.1.0=0
  - cuda-version=12.9=3
  - ffmpeg=4.3=hf484d3e_0
  - filelock=3.17.0=py39h06a4308_0
  - freetype=2.13.3=h4a9f257_0
  - giflib=5.2.2=h5eee18b_0
  - gmp=6.3.0=h6a678d5_0
  - gmpy2=2.2.1=py39h5eee18b_0
  - gnutls=3.6.15=he1e5248_0
  - idna=3.7=py39h06a4308_0
  - intel-openmp=2023.1.0=hdb19cb5_46306
  - jinja2=3.1.6=py39h06a4308_0
  - jpeg=9e=h5eee18b_3
  - lame=3.100=h7b6447c_0
  - lcms2=2.16=h92b89f2_1
  - ld_impl_linux-64=2.40=h12ee557_0
  - lerc=4.0.0=h6a678d5_0
  - libcublas=*********=0
  - libcufft=********=0
  - libcufile=*********=4
  - libcurand=**********=0
  - libcusolver=*********=0
  - libcusparse=*********=0
  - libdeflate=1.22=h5eee18b_0
  - libffi=3.4.4=h6a678d5_1
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libiconv=1.16=h5eee18b_3
  - libidn2=2.3.4=h5eee18b_0
  - libjpeg-turbo=2.0.0=h9bf148f_0
  - libnpp=12.0.2.50=0
  - libnvjitlink=12.1.105=0
  - libnvjpeg=12.1.1.14=0
  - libpng=1.6.39=h5eee18b_0
  - libstdcxx-ng=11.2.0=h1234567_1
  - libtasn1=4.19.0=h5eee18b_0
  - libtiff=4.7.0=hde9077f_0
  - libunistring=0.9.10=h27cfd23_0
  - libwebp=1.3.2=h9f374a3_1
  - libwebp-base=1.3.2=h5eee18b_1
  - llvm-openmp=14.0.6=h9e868ea_0
  - lz4-c=1.9.4=h6a678d5_1
  - markupsafe=3.0.2=py39h5eee18b_0
  - mkl=2023.1.0=h213fc3f_46344
  - mkl-service=2.4.0=py39h5eee18b_2
  - mkl_fft=1.3.11=py39h5eee18b_0
  - mkl_random=1.2.8=py39h1128e8f_0
  - mpc=1.3.1=h5eee18b_0
  - mpfr=4.2.1=h5eee18b_0
  - mpmath=1.3.0=py39h06a4308_0
  - ncurses=6.4=h6a678d5_0
  - nettle=3.7.3=hbbd107a_1
  - networkx=3.2.1=py39h06a4308_0
  - numpy-base=2.0.1=py39hb5e798b_1
  - ocl-icd=2.3.2=h5eee18b_1
  - openh264=2.1.1=h4ff587b_0
  - openjpeg=2.5.2=h0d4d230_1
  - openssl=3.0.16=h5eee18b_0
  - pillow=11.1.0=py39hac6e08b_1
  - pip=25.1=pyhc872135_2
  - pysocks=1.7.1=py39h06a4308_0
  - python=3.9.21=he870216_1
  - pytorch=2.5.1=py3.9_cuda12.1_cudnn9.1.0_0
  - pytorch-cuda=12.1=ha16c6d3_6
  - pytorch-mutex=1.0=cuda
  - pyyaml=6.0.2=py39h5eee18b_0
  - readline=8.2=h5eee18b_0
  - requests=2.32.3=py39h06a4308_1
  - setuptools=78.1.1=py39h06a4308_0
  - sqlite=3.45.3=h5eee18b_0
  - tbb=2021.8.0=hdb19cb5_0
  - tk=8.6.14=h39e8969_0
  - torchaudio=2.5.1=py39_cu121
  - torchtriton=3.1.0=py39
  - torchvision=0.20.1=py39_cu121
  - typing_extensions=4.12.2=py39h06a4308_0
  - urllib3=2.3.0=py39h06a4308_0
  - wheel=0.45.1=py39h06a4308_0
  - xz=5.6.4=h5eee18b_1
  - yaml=0.2.5=h7b6447c_0
  - zlib=1.2.13=h5eee18b_1
  - zstd=1.5.6=hc292b87_0
  - pip:
      - absl-py==2.2.2
      - aiohappyeyeballs==2.6.1
      - aiohttp==3.11.18
      - aiosignal==1.3.2
      - albucore==0.0.24
      - albumentations==2.0.6
      - annotated-types==0.7.0
      - async-timeout==5.0.1
      - attrs==25.3.0
      - contourpy==1.3.0
      - cycler==0.12.1
      - eval-type-backport==0.2.2
      - fonttools==4.58.0
      - frozenlist==1.6.0
      - fsspec==2025.3.2
      - grpcio==1.71.0
      - imageio==2.37.0
      - importlib-metadata==8.7.0
      - importlib-resources==6.5.2
      - kiwisolver==1.4.7
      - lazy-loader==0.4
      - lightning-utilities==0.14.3
      - markdown==3.8
      - matplotlib==3.9.4
      - multidict==6.4.3
      - numpy==2.0.2
      - opencv-python-headless==*********
      - packaging==25.0
      - pandas==2.2.3
      - propcache==0.3.1
      - protobuf==6.30.2
      - pydantic==2.11.4
      - pydantic-core==2.33.2
      - pyparsing==3.2.3
      - python-dateutil==2.9.0.post0
      - pytorch-lightning==2.5.1.post0
      - pytz==2025.2
      - scikit-image==0.24.0
      - scipy==1.13.1
      - simsimd==6.2.1
      - six==1.17.0
      - stringzilla==3.12.5
      - sympy==1.13.1
      - tensorboard==2.19.0
      - tensorboard-data-server==0.7.2
      - tifffile==2024.8.30
      - torchinfo==1.8.0
      - torchmetrics==1.7.1
      - tqdm==4.67.1
      - typing-inspection==0.4.0
      - tzdata==2025.2
      - werkzeug==3.1.3
      - yarl==1.20.0
      - zipp==3.21.0
prefix: ${CONDA_PREFIX}/envs/gaze-training-pipeline
