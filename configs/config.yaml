# Gaze Tracking Pipeline Configuration

# Camera parameters
camera:
  intrinsics_path: "configs/depth_camera_calibration.yaml"  # Intrinsic parameters of the depth camera

# Training parameters
training:
  train: false               # Whether to train the model
  test: false                # Whether to test the model
  epochs: 100                # Number of training epochs
  batch_size: 16             # Batch size for training
  val_split: 0.1             # Validation split ratio
  learning_rate: 0.00005     # Learning rate for optimizer
  weight_decay: 0.0001       # L2 regularization coefficient
  gpus: 1                    # Number of GPUs to use
  seed: 42                   # Random seed for reproducibility
  print_interval: 1         # Interval for printing metrics
  save_dir: "runs"           # Directory to save logs and checkpoints
  loss_weights:              # Weights for different loss components
    angular: 1.0             # Weight for angular loss
    l1: 0.1                  # Weight for L1 loss
  dataset_path: "datasets/DeltaX/dataset_normalized_v2"        # Dataset path for normalized data
  checkpoint_path: weights/author_subject-independent.ckpt  # Path to pretrained model (null for training from scratch)

# Data augmentation parameters
augmentation:
  enabled: true              # Whether to use data augmentation
  shift_limit: 0.2           # Maximum shift as a fraction of image size
  scale_limit: 0.1           # Maximum scale factor
  rotate_limit: 10           # Maximum rotation angle in degrees
  brightness_contrast: false # Whether to use brightness/contrast augmentation
  blur: false                # Whether to use blur augmentation

# Logging parameters
logging:
  tensorboard: true          # Whether to use TensorBoard
  log_images: true           # Whether to log images to TensorBoard
  log_frequency: 1           # Frequency of logging (in steps)
  save_model_frequency: 5    # Frequency of saving model checkpoints (in epochs)

# Data preprocessing parameters
data_preprocessing:
  dataset_path: "datasets/DeltaX/dataset_raw_v2"               # Input path for raw data
  output_path: "datasets/DeltaX/dataset_normalized_v2" # Output path for normalized data

# Data distribution parameters
data_distribution:
  dataset_path: "datasets/DeltaX/dataset_normalized_v2"  # Input path for normalized data
  output_path: "outputs/data_distribution_results_v2"    # Output path for plots and statistics

# Gaze visualization parameters
gaze_directions_visualization:
  dataset_path: "datasets/DeltaX/dataset_normalized_v2"        # Input path for normalized data (gaze ground truth)
  output_path: "outputs/gaze_visualization_results_v2"         # Output path for plots and statistics
  model_ckpt_path: "runs/version_42/checkpoints/best.ckpt"  # Model path for gaze prediction
  arrow_length: 50
  arrow_thickness: 2
