# NaN Loss Troubleshooting Guide

This guide helps you diagnose and fix NaN (Not a Number) losses that occur during training of the gaze tracking model.

## 🔍 Quick Diagnosis

Run the debugging script first:
```bash
python scripts/debug_nan_issues.py
```

## 🚨 Common Causes and Solutions

### 1. **Angular Loss Numerical Instability**

**Problem**: The `angular_loss` function uses `torch.arccos()` which can produce NaN if cosine similarity values are outside [-1, 1].

**Solution**: ✅ **FIXED** - The angular loss function now includes:
- Input validation and clamping
- Vector normalization
- Numerical stability improvements
- Graceful error handling

### 2. **Gradient Explosion**

**Problem**: Large gradients cause parameter updates that lead to NaN values.

**Solution**: ✅ **FIXED** - Added gradient clipping:
```python
# In trainer configuration
gradient_clip_val=1.0
gradient_clip_algorithm='norm'
```

### 3. **Learning Rate Too High**

**Problem**: High learning rates can cause unstable training.

**Solution**: ✅ **FIXED** - Reduced learning rate:
- Old: `5e-5`
- New: `1e-5` (5x smaller)

### 4. **Loss Weight Imbalance**

**Problem**: Angular loss can dominate and cause instability.

**Solution**: ✅ **FIXED** - Rebalanced loss weights:
```yaml
loss_weights:
  angular: 0.5  # Reduced from 1.0
  l1: 1.0       # Increased from 0.1
```

### 5. **Mixed Precision Issues**

**Problem**: Half-precision (16-bit) training can cause numerical instability.

**Solution**: ✅ **FIXED** - Using full precision:
```python
precision='32-true'  # Full 32-bit precision
```

## 🛠️ Implementation Details

### Enhanced Error Detection

The training loop now includes comprehensive NaN detection:

```python
# Input validation
if torch.isnan(full_face_image).any():
    print(f"WARNING: NaN detected in face images at epoch {self.current_epoch}")

# Output validation  
if torch.isnan(outputs).any():
    print(f"WARNING: NaN detected in model outputs at epoch {self.current_epoch}")
    outputs = torch.where(torch.isnan(outputs), torch.zeros_like(outputs), outputs)

# Loss validation
if torch.isnan(loss):
    print(f"CRITICAL: Final loss is invalid at epoch {self.current_epoch}")
    loss = torch.tensor(1e-6, device=outputs.device, requires_grad=True)
```

### Improved Optimizer Configuration

```python
optimizer = torch.optim.Adam(
    self.parameters(),
    lr=self.learning_rate,
    weight_decay=self.weight_decay,
    eps=1e-8,  # Prevent division by zero
    betas=(0.9, 0.999)
)

# Learning rate scheduler for adaptive learning
scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
    optimizer,
    mode='min',
    factor=0.5,
    patience=10,
    min_lr=1e-7
)
```

### Robust Angular Loss Function

The angular loss now includes:
- Input range clamping: `torch.clamp(angles, -π, π)`
- Vector normalization: `F.normalize(vectors, eps=1e-8)`
- Cosine similarity safety: `torch.clamp(cosine_sim, -1+ε, 1-ε)`
- Multiple validation checkpoints
- Graceful fallback to zero loss on errors

## 🔧 Training Configuration

### Recommended Settings

```yaml
training:
  learning_rate: 0.00001      # Reduced for stability
  weight_decay: 0.0001        # Added regularization
  loss_weights:
    angular: 0.5              # Reduced dominance
    l1: 1.0                   # Increased stability
```

### Trainer Settings

```python
trainer = Trainer(
    gradient_clip_val=1.0,           # Prevent gradient explosion
    gradient_clip_algorithm='norm',   # Use L2 norm clipping
    detect_anomaly=True,             # Enable anomaly detection
    precision='32-true',             # Full precision
    deterministic=True,              # Reproducible results
)
```

## 📊 Monitoring and Debugging

### Real-time Monitoring

The training now prints detailed warnings when issues are detected:
- Input data validation
- Model output validation  
- Loss function validation
- Gradient monitoring

### Debug Script Usage

```bash
# Run comprehensive debugging
python scripts/debug_nan_issues.py

# Check specific components
python -c "
from scripts.debug_nan_issues import check_data_quality, test_loss_functions
# Your debugging code here
"
```

## 🎯 Prevention Strategies

### 1. **Data Preprocessing**
- Ensure normalized data is within expected ranges
- Check for corrupted images or labels
- Validate depth data quality

### 2. **Model Architecture**
- Use batch normalization where appropriate
- Initialize weights properly
- Avoid extreme activation functions

### 3. **Training Process**
- Start with lower learning rates
- Use learning rate scheduling
- Monitor gradients and losses
- Save checkpoints frequently

### 4. **Loss Function Design**
- Use numerically stable implementations
- Add epsilon values for divisions
- Clamp intermediate values
- Provide fallback mechanisms

## 🚀 Quick Fix Checklist

If you encounter NaN losses:

1. ✅ **Update the code** with the fixes provided
2. ✅ **Use the new configuration** with reduced learning rate
3. ✅ **Enable gradient clipping** in trainer
4. ✅ **Run the debug script** to identify specific issues
5. ✅ **Monitor training logs** for warning messages
6. ✅ **Use full precision** training
7. ✅ **Check data quality** before training

## 📈 Expected Results

After implementing these fixes:
- **Stable training**: No more NaN losses
- **Better convergence**: More consistent loss reduction
- **Improved robustness**: Handles edge cases gracefully
- **Better monitoring**: Clear warnings when issues occur

## 🆘 If Problems Persist

1. **Run the debug script**: `python scripts/debug_nan_issues.py`
2. **Check data quality**: Verify your dataset doesn't contain corrupted files
3. **Reduce learning rate further**: Try `5e-6` or `1e-6`
4. **Increase regularization**: Try `weight_decay=0.001`
5. **Use different loss weights**: Try `angular: 0.1, l1: 1.0`

The implemented fixes address the most common causes of NaN losses in gaze tracking training. Your training should now be much more stable and robust!
