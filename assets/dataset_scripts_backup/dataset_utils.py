import cv2
import yaml
import sys
import numpy as np
import matplotlib.pyplot as plt

from enum import Enum
from typing import Tuple, Union


FONT = cv2.FONT_HERSHEY_SIMPLEX
TEXT_SCALE = 0.5
TEXT_THICKNESS = 2

class TargetOrientation(Enum):
    UP = 82
    DOWN = 84
    LEFT = 81
    RIGHT = 83
    
    
    
def get_camera_matrix(intrinsics_path: str) -> Tuple[np.ndarray, np.ndarray]:
    """
    Load camera_matrix and dist_coefficients from `intrinsics_path`.

    :param base_path: base path of data
    :return: camera intrinsic matrix and dist_coefficients
    """
    with open(intrinsics_path, 'r') as file:
        calibration_matrix = yaml.safe_load(file)
    camera_matrix = np.asarray(calibration_matrix['camera_matrix']).reshape(3, 3)
    dist_coefficients = np.asarray(calibration_matrix['dist_coeff'])
    return camera_matrix, dist_coefficients


def get_face_landmarks_in_ccs_from_depth(camera_matrix, depth_frame, face_landmarks_2d):
    """
    Convert 2D face landmarks into 3D camera coordinate system using depth data.

    :param camera_matrix: Intrinsic camera matrix (3x3)
    :param depth_frame: Depth frame from RealSense camera (numpy array)
    :param face_landmarks_2d: 2D facial landmarks (Nx2 array of [u, v] pixel coordinates)
    :return: 3D facial landmarks in the camera coordinate system (3xN numpy array)
    """
    fx = camera_matrix[0, 0]  # Focal length in x
    fy = camera_matrix[1, 1]  # Focal length in y
    cx = camera_matrix[0, 2]  # Principal point x
    cy = camera_matrix[1, 2]  # Principal point y

    landmarks_3d = []
    
    for (u, v) in face_landmarks_2d:
        # Ensure integer pixel coordinates and valid depth retrieval
        u, v = int(round(u)), int(round(v))

        if u < 0 or u >= depth_frame.shape[1] or v < 0 or v >= depth_frame.shape[0]:
            landmarks_3d.append([0, 0, 0])  # Out-of-bounds handling
            continue

        # Get the depth at the landmark pixel
        d = depth_frame[v, u]  # Depth in meters

        if d == 0:  # If depth is invalid, skip
            landmarks_3d.append([0, 0, 0])
            continue

        # Convert from pixel coordinates (u, v) plus depth d to 3D coordinates
        X = (u - cx) * d / fx
        
        Y = (v - cy) * d / fy
        Z = d
        landmarks_3d.append([X, Y, Z])

    return np.array(landmarks_3d).T  # Shape: (3, N)


def gaze_2d_to_3d(gaze: np.ndarray) -> np.ndarray:
    """
    pitch and gaze to 3d vector

    :param gaze: pitch and gaze vector
    :return: 3d vector
    """
    x = -np.cos(gaze[0]) * np.sin(gaze[1])
    y = -np.sin(gaze[0])
    z = -np.cos(gaze[0]) * np.cos(gaze[1])
    return np.array([x, y, z])



def get_point_on_screen(monitor_mm: Tuple[float, float], monitor_pixels: Tuple[float, float], result: np.ndarray) -> Tuple[int, int]:
    """
    Calculate point in screen in pixels.

    :param monitor_mm: dimensions of the monitor in mm
    :param monitor_pixels: dimensions of the monitor in pixels
    :param result: predicted point on the screen in mm
    :return: point in screen in pixels
    """
    result_x = result[0]
    result_x = -result_x + monitor_mm[0] / 2
    result_x = result_x * (monitor_pixels[0] / monitor_mm[0])

    result_y = result[1]
    result_y = result_y - 20  # 20 mm offset
    result_y = min(result_y, monitor_mm[1])
    result_y = result_y * (monitor_pixels[1] / monitor_mm[1])

    return tuple(np.asarray([result_x, result_y]).round().astype(int))


def cleanup(source, plot_3d_scene):
    """Forcefully stop all running processes."""
    print("\nCleaning up resources...")

    try:
        cv2.destroyAllWindows()
        plt.close('all')
        
        if plot_3d_scene:
            plot_3d_scene.close_figure()

        if source:
            source.stop()
        
        print("Cleanup complete. Exiting program.")

    except Exception as e:
        print(f"Error during cleanup: {e}")

    sys.exit(0)  # Ensure the program terminates


def compute_rvec_from_landmarks(landmarks_3d):
    """
    Compute head rotation vector (rvec) from 3D facial landmarks in camera coordinate system.

    :param landmarks_3d: np.ndarray of shape (3, N), 3D facial landmarks in camera space
    :return: rvec (3x1 rotation vector), rotation_matrix (3x3)
    """
    # Define landmark indices
    right_eye_indices = [33, 133]      # Subject's right eye (from camera view: left)
    left_eye_indices = [362, 263]      # Subject's left eye (from camera view: right)
    mouth_indices = [61, 291]          # Mouth corners

    # Compute key points
    right_eye = np.mean(landmarks_3d[:, right_eye_indices], axis=1)
    left_eye = np.mean(landmarks_3d[:, left_eye_indices], axis=1)
    mouth_center = np.mean(landmarks_3d[:, mouth_indices], axis=1)
    eye_center = 0.5 * (left_eye + right_eye)

    # X-axis: from right eye to left eye
    x_axis = left_eye - right_eye
    
    x_axis /= np.linalg.norm(x_axis)

    # Y-axis: from eye center to mouth center
    y_axis = mouth_center - eye_center
    y_axis /= np.linalg.norm(y_axis)

    # Z-axis: orthogonal vector (forward direction)
    z_axis = np.cross(x_axis, y_axis)
    z_axis /= np.linalg.norm(z_axis)

    # Re-orthogonalize y_axis to ensure strict orthogonality
    y_axis = np.cross(z_axis, x_axis)
    y_axis /= np.linalg.norm(y_axis)

    # Rotation matrix: columns are the axes of face in camera space
    R = np.stack([x_axis, y_axis, z_axis], axis=1)  # Shape (3, 3)
    # print(f"valid axes:x-y{np.dot(x_axis, y_axis)}, y-z:{np.dot(y_axis,z_axis)}, z-x:{np.dot(z_axis,x_axis)}")
    # Convert rotation matrix to rotation vector (Rodrigues)
    rvec, _ = cv2.Rodrigues(R)

    return rvec, R



def get_face_landmarks_in_ccs(camera_matrix, dist_coefficients, shape, results, face_model, face_model_all, landmarks_ids):
    """
    Fit `face_model` onto `face_landmarks` using `solvePnP`.

    :param camera_matrix: camera intrinsic matrix
    :param dist_coefficients: distortion coefficients
    :param shape: image shape
    :param results: output of MediaPipe FaceMesh
    :return: full face model in the camera coordinate system
    """
    height, width, _ = shape
    face_landmarks = np.asarray([[landmark.x * width, landmark.y * height] for landmark in results.multi_face_landmarks[0].landmark])
    face_landmarks = np.asarray([face_landmarks[i] for i in landmarks_ids])

    rvec, tvec = None, None
    success, rvec, tvec, inliers = cv2.solvePnPRansac(face_model, face_landmarks, camera_matrix, dist_coefficients, rvec=rvec, tvec=tvec, useExtrinsicGuess=True, flags=cv2.SOLVEPNP_EPNP)  # Initial fit
    for _ in range(10):
        success, rvec, tvec = cv2.solvePnP(face_model, face_landmarks, camera_matrix, dist_coefficients, rvec=rvec, tvec=tvec, useExtrinsicGuess=True, flags=cv2.SOLVEPNP_ITERATIVE)  # Second fit for higher accuracy

    head_rotation_matrix, _ = cv2.Rodrigues(rvec.reshape(-1))
    return np.dot(head_rotation_matrix, face_model.T) + tvec.reshape((3, 1)), np.dot(head_rotation_matrix, face_model_all.T) + tvec.reshape((3, 1))  # 3D positions of facial landmarks
