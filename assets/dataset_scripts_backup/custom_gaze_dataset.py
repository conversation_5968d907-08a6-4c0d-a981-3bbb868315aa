import torch
import skimage.io
import numpy as np
import pandas as pd
import albumentations as A

from albumentations.pytorch import ToTensorV2
from torch.utils.data import Dataset, DataLoader


class CustomGazeDataset(Dataset):
    """
    Dataset for custom normalized gaze data saved as images and a labels CSV.

    Expects a directory structure:
      <data_root>/labels.csv
      <data_root>/pXX/<basename>-face.jpg
      <data_root>/pXX/<basename>-left-eye.jpg
      <data_root>/pXX/<basename>-right-eye.jpg

    CSV columns: face_file_name, left_eye, right_eye, pitch, yaw
    """
    def __init__(self, data_root: str, transform=None):
        self.data_root = data_root
        self.transform = transform
        self.df = pd.read_csv(f"{data_root}/labels.csv")

    def __len__(self):
        return len(self.df)

    def __getitem__(self, idx):
        row = self.df.iloc[idx]

        # Derive person_idx from session prefix 'pXX/...'
        session = row.face_file_name.split('/')[0]
        person_idx = int(session[1:])  # 'p00' -> 0
        person_idx = torch.tensor(person_idx, dtype=torch.long)
        
        # Load images
        face = skimage.io.imread(f"{self.data_root}/{row.face_file_name}")
        left = skimage.io.imread(f"{self.data_root}/{row.left_eye}")
        right = skimage.io.imread(f"{self.data_root}/{row.right_eye}")

        # Apply transforms (optional: flipping or augmentations)
        face_tensor = self.transform(image=face)["image"]
        left_tensor = self.transform(image=left)["image"]
        right_tensor = self.transform(image=right)["image"]

        # Get labels
        pitch = torch.tensor(row.pitch, dtype=torch.float32)
        yaw = torch.tensor(row.yaw, dtype=torch.float32)

        return {
            'person_idx':      person_idx,
            'full_face_image': face_tensor,
            'left_eye_image':  left_tensor,
            'right_eye_image': right_tensor,
            'gaze_pitch':      pitch,
            'gaze_yaw':        yaw
        }


def get_dataloaders(data_root: str,
                    batch_size: int = 16,
                    val_split: float = 0.1,
                    test_split: float = 0.1,
                    test_only: bool = False):
    """
    Create train/val/test dataloaders by splitting the CSV rows.

    :param data_root: root directory containing `labels.csv` and image sub-folders
    :param batch_size: batch size for all loaders
    :param val_split: fraction for validation set
    :param test_split: fraction for test set
    :param test_only: if True, return test loader with full dataset and no split

    :return: train_loader, val_loader, test_loader
    """
    # Create val, test transforms
    transform_val = transform_test = A.Compose([
        A.Normalize(),
        ToTensorV2()
    ])
    
    # return test loader with full dataset
    if test_only:
        full_dataset = CustomGazeDataset(data_root, transform=transform_test)
        test_loader = DataLoader(full_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
        return None, None, test_loader
    
    # Create train transforms
    transform_train = A.Compose([
        A.ShiftScaleRotate(p=1.0,
                           shift_limit=0.2,
                           scale_limit=0.1,
                           rotate_limit=10),
        A.Normalize(),
        ToTensorV2()
    ])
    
    # Split dataset
    df = pd.read_csv(f"{data_root}/labels.csv")
    
    n = len(df)
    indices = list(range(n))
    np.random.shuffle(indices)

    val_size = int(n * val_split)
    test_size = int(n * test_split)
    
    test_idx = indices[:test_size]
    val_idx = indices[test_size:test_size + val_size]
    train_idx = indices[test_size + val_size:]

    # Create three datasets with different transforms
    train_ds = torch.utils.data.Subset(
        CustomGazeDataset(data_root, transform=transform_train), train_idx
    )
    val_ds = torch.utils.data.Subset(
        CustomGazeDataset(data_root, transform=transform_val), val_idx
    )
    test_ds = torch.utils.data.Subset(
        CustomGazeDataset(data_root, transform=transform_test), test_idx
    )

    # Create dataloaders
    train_loader = DataLoader(train_ds, batch_size=batch_size, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_ds, batch_size=batch_size, shuffle=False, num_workers=4)
    test_loader = DataLoader(test_ds, batch_size=batch_size, shuffle=False, num_workers=4)

    return train_loader, val_loader, test_loader
