import sys
from typing import Tuple

import cv2
import numpy as np
import matplotlib.pyplot as plt


# ===========================================================================
# NOT USED
# ===========================================================================
def get_face_landmarks_in_ccs(camera_matrix,
                              dist_coefficients,
                              shape,
                              results,
                              face_model,
                              face_model_all,
                              landmarks_ids):
    """
    Fit `face_model` onto `face_landmarks` using `solvePnP`.

    :param camera_matrix: camera intrinsic matrix
    :param dist_coefficients: distortion coefficients
    :param shape: image shape
    :param results: output of MediaPipe FaceMesh
    :return: full face model in the camera coordinate system
    """
    h, w, _ = shape
    landmarks = results.multi_face_landmarks[0].landmark
    face_landmarks = np.asarray([[lmk.x * w, lmk.y * h] for lmk in landmarks])
    face_landmarks = np.asarray([face_landmarks[i] for i in landmarks_ids])

    rvec, tvec = None, None

    # Initial fit using RANSAC for robustness against outliers
    _, rvec, tvec, _ = cv2.solvePnPRansac(
        face_model,
        face_landmarks,
        camera_matrix,
        dist_coefficients,
        rvec=rvec,
        tvec=tvec,
        useExtrinsicGuess=True,
        flags=cv2.SOLVEPNP_EPNP
    )

    # Refine the pose estimation with iterative optimization
    for _ in range(10):
        _, rvec, tvec = cv2.solvePnP(
            face_model,
            face_landmarks,
            camera_matrix,
            dist_coefficients,
            rvec=rvec,
            tvec=tvec,
            useExtrinsicGuess=True,
            flags=cv2.SOLVEPNP_ITERATIVE
    )

    head_rotation_matrix, _ = cv2.Rodrigues(rvec.reshape(-1))

    result = (np.dot(head_rotation_matrix, face_model.T) + tvec.reshape((3, 1)),
              np.dot(head_rotation_matrix, face_model_all.T) + tvec.reshape((3, 1))
    )  # 3D positions of facial landmarks

    return result


def gaze_2d_to_3d(gaze: np.ndarray) -> np.ndarray:
    """
    pitch and gaze to 3d vector

    :param gaze: pitch and gaze vector
    :return: 3d vector
    """
    x = -np.cos(gaze[0]) * np.sin(gaze[1])
    y = -np.sin(gaze[0])
    z = -np.cos(gaze[0]) * np.cos(gaze[1])
    return np.array([x, y, z])


def get_point_on_screen(monitor_mm: Tuple[float, float],
                        monitor_pixels: Tuple[float, float],
                        result: np.ndarray) -> Tuple[int, int]:
    """
    Calculate point in screen in pixels.

    :param monitor_mm: dimensions of the monitor in mm
    :param monitor_pixels: dimensions of the monitor in pixels
    :param result: predicted point on the screen in mm
    :return: point in screen in pixels
    """
    result_x = result[0]
    result_x = -result_x + monitor_mm[0] / 2
    result_x = result_x * (monitor_pixels[0] / monitor_mm[0])

    result_y = result[1]
    result_y = result_y - 20  # 20 mm offset
    result_y = min(result_y, monitor_mm[1])
    result_y = result_y * (monitor_pixels[1] / monitor_mm[1])

    return tuple(np.asarray([result_x, result_y]).round().astype(int))


def cleanup(source, plot_3d_scene):
    """Forcefully stop all running processes."""
    print("\nCleaning up resources...")

    try:
        cv2.destroyAllWindows()
        plt.close('all')

        if plot_3d_scene:
            plot_3d_scene.close_figure()

        if source:
            source.stop()

        print("Cleanup complete. Exiting program.")

    except Exception as e:
        print(f"Error during cleanup: {e}")

    sys.exit(0)  # Ensure the program terminates
# ===========================================================================
# NOT USED
# ===========================================================================