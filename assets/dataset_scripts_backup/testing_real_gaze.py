import cv2
import numpy as np
import mediapipe as mp
import pyrealsense2 as rs
import matplotlib.pyplot as plt

from screeninfo import get_monitors
from mpl_toolkits.mplot3d import Axes3D


class TVFaceTracker:
    def __init__(self):
        # TV corners in camera coordinates (x, y, z) in mm
        self.tv_corners = {
            "top_left": np.array([290, 25, 15]),
            "top_right": np.array([-330, 25, 15]),
            "bottom_right": np.array([-330, 366, 15]),
            "bottom_left": np.array([290, 366, 15]),
        }
        
        # Calculate TV dimensions
        self.tv_width = np.linalg.norm(self.tv_corners["top_right"] - self.tv_corners["top_left"])
        self.tv_height = np.linalg.norm(self.tv_corners["bottom_left"] - self.tv_corners["top_left"])
        
        # Define TV resolution (you can adjust this to match your TV)
        self.tv_resolution = (1920, 1080)  # Width, Height in pixels
        
        # Calculate mm per pixel
        self.mm_per_pixel_x = self.tv_width / self.tv_resolution[0]
        self.mm_per_pixel_y = self.tv_height / self.tv_resolution[1]
        
        # Calculate TV coordinate system
        self.tv_origin = self.tv_corners["top_left"]
        
        # TV axes (normalized)
        self.tv_x_axis = (self.tv_corners["top_right"] - self.tv_corners["top_left"])
        self.tv_x_axis = self.tv_x_axis / np.linalg.norm(self.tv_x_axis)
        
        self.tv_y_axis = (self.tv_corners["bottom_left"] - self.tv_corners["top_left"])
        self.tv_y_axis = self.tv_y_axis / np.linalg.norm(self.tv_y_axis)
        
        self.tv_z_axis = np.cross(self.tv_x_axis, self.tv_y_axis)
        self.tv_z_axis = self.tv_z_axis / np.linalg.norm(self.tv_z_axis)
        
        # Create rotation matrix from camera to TV
        self.R_cam_to_tv = np.column_stack((self.tv_x_axis, self.tv_y_axis, self.tv_z_axis))
        
        # Initialize MediaPipe Face Mesh
        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        
        # MediaPipe indices for eye landmarks
        self.LEFT_EYE_CENTER = 468
        self.RIGHT_EYE_CENTER = 473
        
        # Target point (circle) radius in cm
        self.target_radius_cm = 5
        self.target_radius_mm = self.target_radius_cm * 10  # Convert to mm
        
        # Initialize target position at center of TV
        self.target_position_tv = np.array([self.tv_width / 2, self.tv_height / 2, 0])
        self.target_velocity = np.array([5, 5, 0])  # mm per frame
        
        # Initialize RealSense pipeline
        self.pipeline = rs.pipeline()
        self.config = rs.config()
        
        # Enable streams
        self.config.enable_stream(rs.stream.depth, 640, 480, rs.format.z16, 30)
        self.config.enable_stream(rs.stream.color, 640, 480, rs.format.bgr8, 30)
        
        # For moving average of face landmarks (smoothing)
        self.face_history = []
        self.history_length = 5
        
        # Projection length for 2D gaze visualization
        self.gaze_projection_length = 300  # mm
        
    def camera_to_tv_coordinates(self, point_camera):
        """Convert a point from camera coordinates to TV coordinates"""
        # Vector from TV origin to the point
        rel_vector = point_camera - self.tv_origin
        
        # Transform to TV coordinates
        point_tv = np.dot(self.R_cam_to_tv.T, rel_vector)
        
        return point_tv
        
    def tv_to_camera_coordinates(self, point_tv):
        """Convert a point from TV coordinates to camera coordinates"""
        # Transform to camera-relative coordinates
        rel_vector = np.dot(self.R_cam_to_tv, point_tv)
        
        # Add TV origin offset
        point_camera = rel_vector + self.tv_origin
        
        return point_camera
    
    def tv_pixel_to_3d(self, pixel_x, pixel_y):
        """Convert TV pixel coordinates to 3D point in TV coordinates"""
        # Convert pixel to mm on TV plane
        x_mm = pixel_x * self.mm_per_pixel_x
        y_mm = pixel_y * self.mm_per_pixel_y
        
        # 3D point in TV coordinates
        return np.array([x_mm, y_mm, 0])
    
    def move_target(self):
        """Move the target circle and make it bounce off TV edges"""
        # Update position
        self.target_position_tv += self.target_velocity
        
        # Check boundaries (accounting for radius)
        if (self.target_position_tv[0] - self.target_radius_mm < 0 or 
            self.target_position_tv[0] + self.target_radius_mm > self.tv_width):
            self.target_velocity[0] *= -1
            
        if (self.target_position_tv[1] - self.target_radius_mm < 0 or 
            self.target_position_tv[1] + self.target_radius_mm > self.tv_height):
            self.target_velocity[1] *= -1
    
    def draw_target_on_tv(self):
        """Draw the target circle on the TV display in full screen mode"""
        # Create a black image with TV resolution
        tv_image = np.zeros((self.tv_resolution[1], self.tv_resolution[0], 3), dtype=np.uint8)
        
        # Calculate center and radius in pixels
        center_x = int(self.target_position_tv[0] / self.mm_per_pixel_x)
        center_y = int(self.target_position_tv[1] / self.mm_per_pixel_y)
        radius_pixels = int(self.target_radius_mm / self.mm_per_pixel_x)
        
        # Draw circle
        cv2.circle(tv_image, (center_x, center_y), radius_pixels, (0, 0, 255), -1)
        cv2.circle(tv_image, (center_x, center_y), radius_pixels, (255, 255, 255), 2)
        
        # Display the TV image
        cv2.imshow("TV Display", tv_image)
        
        return tv_image
    
    def get_eye_gaze_vectors(self, face_landmarks_3d):
        """Calculate eye gaze vectors based on face landmarks"""
        if face_landmarks_3d is None or len(face_landmarks_3d) == 0:
            return None, None, None, None
        
        # Get eye centers in 3D
        left_eye = face_landmarks_3d[self.LEFT_EYE_CENTER]
        right_eye = face_landmarks_3d[self.RIGHT_EYE_CENTER]
        
        # Get target point in camera coordinates
        target_camera = self.tv_to_camera_coordinates(self.target_position_tv)
        
        # Calculate gaze vectors (normalized)
        left_gaze = target_camera - left_eye
        left_gaze = left_gaze / np.linalg.norm(left_gaze)
        
        right_gaze = target_camera - right_eye
        right_gaze = right_gaze / np.linalg.norm(right_gaze)
        
        return left_eye, left_gaze, right_eye, right_gaze
    
    def project_point_to_image(self, point_3d, intrinsics):
        """Project a 3D point to 2D image coordinates using camera intrinsics"""
        # Convert from mm to meters for RealSense projection
        point_m = point_3d / 1000.0
        
        # Use RealSense to project 3D point to 2D
        pixel = rs.rs2_project_point_to_pixel(intrinsics, point_m)
        
        return int(pixel[0]), int(pixel[1])
    
    def project_gaze_to_image_plane(self, eye_point, gaze_vector, intrinsics, img_width, img_height):
        """Project gaze ray to the image plane using valid camera mathematics
        
        This properly handles the camera projection matrix and ensures valid pixel coordinates
        """
        # Convert from mm to meters for consistency with RealSense
        eye_point_m = eye_point / 1000.0
        
        # We need to find where the gaze ray intersects the image plane
        # Using similar triangles and the pinhole camera model
        
        # Ray from camera origin through the eye point
        # For the RealSense camera, the optical center is the origin
        
        # Calculate image plane intersection using camera projection math
        # Use a range of depths and find where the projected point is within frame
        valid_point = False
        pixel_coords = None
        
        # Try different distances along the gaze ray
        depths = [0.5, 1.0, 2.0, 3.0, 5.0]  # Meters
        
        for depth in depths:
            # Calculate a point along the gaze ray at this depth
            point_on_ray = eye_point_m + gaze_vector * depth
            
            # Project to pixel coordinates
            pixel = rs.rs2_project_point_to_pixel(intrinsics, point_on_ray)
            px, py = int(pixel[0]), int(pixel[1])
            
            # Check if the point is within the image bounds
            if 0 <= px < img_width and 0 <= py < img_height:
                valid_point = True
                pixel_coords = (px, py)
                break
        
        # If no valid projection was found with standard depths, try a very close point
        if not valid_point:
            # Get the raw direction of the gaze in 2D image coordinates
            # Start with unit vector in direction of gaze
            unit_gaze = gaze_vector / np.linalg.norm(gaze_vector)
            
            # Project eye point to image
            eye_pixel = rs.rs2_project_point_to_pixel(intrinsics, eye_point_m)
            eye_px, eye_py = int(eye_pixel[0]), int(eye_pixel[1])
            
            # Create a small offset in the image plane (like 50-100 pixels)
            # This is an approximation for visualization when proper projection fails
            offset_length = 100  # pixels
            
            # Calculate direction in image coordinates based on 3D direction
            # This is a simplified version, but gives reasonable visual results
            dx = unit_gaze[0] 
            dy = unit_gaze[1]
            dz = unit_gaze[2]
            
            # Scale to get reasonable length in image space
            scale = offset_length / max(0.01, abs(dx) + abs(dy))
            
            # Final pixel coordinates
            px = int(eye_px + dx * scale)
            py = int(eye_py + dy * scale)
            
            # Clamp to image boundaries
            px = max(0, min(img_width - 1, px))
            py = max(0, min(img_height - 1, py))
            
            pixel_coords = (px, py)
            
        return pixel_coords
    
    def calculate_target_direction_indicator(self, face_landmarks_3d, intrinsics, img_width, img_height):
        """Calculate a direction indicator for the target even when it's outside camera FOV"""
        if face_landmarks_3d is None or len(face_landmarks_3d) == 0:
            return None

        # Get the center of the face (use nose tip or average of landmarks)
        face_center = np.mean(face_landmarks_3d, axis=0)
        
        # Get target position in camera coordinates
        target_camera = self.tv_to_camera_coordinates(self.target_position_tv)
        
        # Calculate face-to-target vector
        direction_vector = target_camera - face_center
        direction_vector = direction_vector / np.linalg.norm(direction_vector)
        
        # Use our existing function to project this direction to the image plane
        # We're reusing our gaze projection function since the math is the same
        face_center_2d = self.project_point_to_image(face_center, intrinsics)
        
        # Try to find a valid intersection with the image plane
        # Try different distances along the direction ray
        target_indicator = None
        
        # Distances in meters to try
        depths = [0.5, 1.0, 2.0, 3.0, 5.0]
        
        for depth in depths:
            # Calculate point along the direction vector
            point_on_ray = face_center / 1000.0 + direction_vector * depth
            
            # Project to pixel coordinates
            try:
                pixel = rs.rs2_project_point_to_pixel(intrinsics, point_on_ray)
                px, py = int(pixel[0]), int(pixel[1])
                
                # Check if within frame
                if 0 <= px < img_width and 0 <= py < img_height:
                    target_indicator = (px, py)
                    break
            except:
                continue
        
        # If no valid projection, create an indicator at the image edge
        if target_indicator is None:
            # Project the vector to image space
            # For simplicity, get the direction in 2D image space
            
            # First try to project a point at a very close distance
            try:
                point_near = face_center / 1000.0 + direction_vector * 0.1
                pixel_near = rs.rs2_project_point_to_pixel(intrinsics, point_near)
                px_near, py_near = int(pixel_near[0]), int(pixel_near[1])
                
                # Get direction in image space
                dx = px_near - face_center_2d[0]
                dy = py_near - face_center_2d[1]
                
                # Normalize the direction
                length = np.sqrt(dx*dx + dy*dy)
                if length > 0:
                    dx /= length
                    dy /= length
                
                # Find intersection with image boundary
                # We have a ray starting at face_center_2d with direction (dx, dy)
                # We need to find where it intersects the image boundary

                # Calculate times to hit boundaries
                tx_min = (0 - face_center_2d[0]) / dx if dx < 0 else float('inf')
                tx_max = (img_width - 1 - face_center_2d[0]) / dx if dx > 0 else float('inf')
                ty_min = (0 - face_center_2d[1]) / dy if dy < 0 else float('inf')
                ty_max = (img_height - 1 - face_center_2d[1]) / dy if dy > 0 else float('inf')
                
                # Find the smallest positive time
                t = min(filter(lambda x: x > 0, [tx_min, tx_max, ty_min, ty_max]))
                
                # Calculate intersection point
                px = int(face_center_2d[0] + dx * t)
                py = int(face_center_2d[1] + dy * t)
                
                # Ensure within bounds (rounding errors)
                px = max(0, min(img_width - 1, px))
                py = max(0, min(img_height - 1, py))
                
                target_indicator = (px, py)
            except:
                # Fall back to a default direction if all else fails
                # This would be right edge of image at same height as face
                target_indicator = (img_width - 30, face_center_2d[1])
        
        return face_center_2d, target_indicator

    def draw_gaze_on_image(self, color_image, left_eye, left_gaze, right_eye, right_gaze, intrinsics):
        """Draw gaze vectors on the 2D color image"""
        if left_eye is None or right_eye is None:
            return color_image
        
        # Make a copy of the image
        image_with_gaze = color_image.copy()
        img_height, img_width = color_image.shape[:2]
        
        # Project eye centers to 2D
        left_eye_2d = self.project_point_to_image(left_eye, intrinsics)
        right_eye_2d = self.project_point_to_image(right_eye, intrinsics)
        
        # Project gaze vectors to the image plane
        left_gaze_end_2d = self.project_gaze_to_image_plane(
            left_eye, left_gaze, intrinsics, img_width, img_height
        )
        right_gaze_end_2d = self.project_gaze_to_image_plane(
            right_eye, right_gaze, intrinsics, img_width, img_height
        )
        
        # Draw eyes and gaze vectors
        cv2.circle(image_with_gaze, left_eye_2d, 5, (0, 255, 0), -1)  # Left eye - green
        cv2.circle(image_with_gaze, right_eye_2d, 5, (0, 0, 255), -1)  # Right eye - red
        
        # Draw gaze lines with validity check
        if left_gaze_end_2d is not None:
            cv2.line(image_with_gaze, left_eye_2d, left_gaze_end_2d, (0, 255, 0), 2)  # Left gaze - green
            cv2.circle(image_with_gaze, left_gaze_end_2d, 3, (0, 255, 0), -1)  # End point
        
        if right_gaze_end_2d is not None:
            cv2.line(image_with_gaze, right_eye_2d, right_gaze_end_2d, (0, 0, 255), 2)  # Right gaze - red
            cv2.circle(image_with_gaze, right_gaze_end_2d, 3, (0, 0, 255), -1)  # End point
        
        # Calculate average gaze direction
        mid_eye = (left_eye + right_eye) / 2
        mid_gaze = (left_gaze + right_gaze) / 2
        mid_gaze = mid_gaze / np.linalg.norm(mid_gaze)
        
        # Project average gaze vector to image plane
        avg_gaze_end_2d = self.project_gaze_to_image_plane(
            mid_eye, mid_gaze, intrinsics, img_width, img_height
        )
        
        # Draw average gaze vector
        mid_eye_2d = self.project_point_to_image(mid_eye, intrinsics)
        if avg_gaze_end_2d is not None:
            cv2.line(image_with_gaze, mid_eye_2d, avg_gaze_end_2d, (255, 0, 0), 2)  # Average gaze - blue
            cv2.circle(image_with_gaze, avg_gaze_end_2d, 8, (255, 0, 0), -1)  # End point - blue
            
            # Display text for the average gaze point
            cv2.putText(image_with_gaze, "Gaze", (avg_gaze_end_2d[0]+10, avg_gaze_end_2d[1]), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
        
        # Add text labels for eyes
        cv2.putText(image_with_gaze, "Left Eye", (left_eye_2d[0]+10, left_eye_2d[1]), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        cv2.putText(image_with_gaze, "Right Eye", (right_eye_2d[0]+10, right_eye_2d[1]), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
        
        # Draw target direction indicator
        # First, get face landmarks to use as reference
        from_face, to_target = self.calculate_target_direction_indicator(
            [left_eye, right_eye, mid_eye], intrinsics, img_width, img_height
        )
        
        if from_face is not None and to_target is not None:
            # Draw direction arrow from face to target indicator
            cv2.arrowedLine(image_with_gaze, from_face, to_target, (0, 255, 255), 2, tipLength=0.3)
            
            # Show where the target would be (even though it's off-screen)
            cv2.circle(image_with_gaze, to_target, 8, (0, 255, 255), -1)
            
            # Add text
            cv2.putText(image_with_gaze, "Target Direction", (to_target[0]+10, to_target[1]), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
            
            # If we have both gaze and target indicators, calculate angular error
            if avg_gaze_end_2d is not None:
                # Calculate vectors in image space
                gaze_vector = (avg_gaze_end_2d[0] - mid_eye_2d[0], avg_gaze_end_2d[1] - mid_eye_2d[1])
                target_vector = (to_target[0] - from_face[0], to_target[1] - from_face[1])
                
                # Calculate magnitudes
                gaze_mag = np.sqrt(gaze_vector[0]**2 + gaze_vector[1]**2)
                target_mag = np.sqrt(target_vector[0]**2 + target_vector[1]**2)
                
                # Calculate dot product
                if gaze_mag > 0 and target_mag > 0:
                    dot_product = (gaze_vector[0] * target_vector[0] + gaze_vector[1] * target_vector[1]) / (gaze_mag * target_mag)
                    # Clamp to [-1, 1] to avoid numerical errors
                    dot_product = max(-1.0, min(1.0, dot_product))
                    angle_rad = np.arccos(dot_product)
                    angle_deg = np.degrees(angle_rad)
                    
                    # Display angle error
                    cv2.putText(image_with_gaze, f"Angle Error: {angle_deg:.1f}°", (30, 60), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        return image_with_gaze
    
    def process_face_landmarks(self, color_image, depth_frame, intrinsics):
        """Process face landmarks and convert to 3D coordinates"""
        # Convert to RGB for MediaPipe
        rgb_image = cv2.cvtColor(color_image, cv2.COLOR_BGR2RGB)
        
        # Process with MediaPipe
        results = self.face_mesh.process(rgb_image)
        
        face_landmarks_3d = []
        if results.multi_face_landmarks:
            face_landmarks = results.multi_face_landmarks[0]  # First detected face
            
            depth_frame = depth_frame.as_depth_frame()  # Ensure we have a depth frame
            
            # Convert each landmark to 3D using depth
            for idx, landmark in enumerate(face_landmarks.landmark):
                # Convert normalized coordinates to pixel coordinates
                x_px = int(landmark.x * color_image.shape[1])
                y_px = int(landmark.y * color_image.shape[0])
                
                # Ensure within bounds
                if 0 <= x_px < color_image.shape[1] and 0 <= y_px < color_image.shape[0]:
                    # Get depth value at this pixel
                    try:
                        depth_value = depth_frame.get_distance(x_px, y_px)
                        if depth_value <= 0:
                            depth_value = 0.6  # Default depth (600mm)
                    except:
                        depth_value = 0.6  # Default depth (600mm)
                    
                    # Deproject to 3D point
                    point_3d = rs.rs2_deproject_pixel_to_point(intrinsics, [x_px, y_px], depth_value)
                    # Convert to mm
                    face_landmarks_3d.append(np.array([point_3d[0] * 1000, point_3d[1] * 1000, point_3d[2] * 1000]))
                else:
                    # Default position
                    face_landmarks_3d.append(np.array([0, 0, 600]))
            
            # Apply moving average for smoothing
            if len(self.face_history) >= self.history_length:
                self.face_history.pop(0)
            self.face_history.append(face_landmarks_3d)
            
            # Compute average
            if len(self.face_history) > 0:
                avg_landmarks = np.mean(self.face_history, axis=0)
                face_landmarks_3d = avg_landmarks
            
        return face_landmarks_3d
    
    def update_3d_plot(self, face_landmarks_3d):
        """Update the 3D matplotlib visualization"""
        self.ax.clear()
        
        # Set axis labels
        self.ax.set_xlabel('X (mm)')
        self.ax.set_ylabel('Y (mm)')
        self.ax.set_zlabel('Z (mm)')
        self.ax.set_title('3D Face and TV Tracking')
        
        # Draw TV corners and plane
        tv_corners_list = [
            self.tv_corners["top_left"], 
            self.tv_corners["top_right"], 
            self.tv_corners["bottom_right"], 
            self.tv_corners["bottom_left"]
        ]
        
        tv_x = [p[0] for p in tv_corners_list + [tv_corners_list[0]]]
        tv_y = [p[1] for p in tv_corners_list + [tv_corners_list[0]]]
        tv_z = [p[2] for p in tv_corners_list + [tv_corners_list[0]]]
        
        self.ax.plot(tv_x, tv_y, tv_z, 'b-', linewidth=2, label='TV')
        
        # Draw TV coordinate axes
        axis_length = 100  # mm
        origin = self.tv_origin
        
        self.ax.quiver(origin[0], origin[1], origin[2], 
                      self.tv_x_axis[0], self.tv_x_axis[1], self.tv_x_axis[2], 
                      color='r', length=axis_length, label='TV X-axis')
        self.ax.quiver(origin[0], origin[1], origin[2], 
                      self.tv_y_axis[0], self.tv_y_axis[1], self.tv_y_axis[2], 
                      color='g', length=axis_length, label='TV Y-axis')
        self.ax.quiver(origin[0], origin[1], origin[2], 
                      self.tv_z_axis[0], self.tv_z_axis[1], self.tv_z_axis[2], 
                      color='b', length=axis_length, label='TV Z-axis')
        
        # Draw target point
        target_camera = self.tv_to_camera_coordinates(self.target_position_tv)
        self.ax.scatter(target_camera[0], target_camera[1], target_camera[2], 
                       color='red', s=100, label='Target')
        
        # Draw face landmarks if available
        if len(face_landmarks_3d) > 0:
            # Extract eye landmarks
            left_eye, left_gaze, right_eye, right_gaze = self.get_eye_gaze_vectors(face_landmarks_3d)
            
            if left_eye is not None and right_eye is not None:
                # Plot eyes
                self.ax.scatter(left_eye[0], left_eye[1], left_eye[2], 
                               color='green', s=50, label='Left Eye')
                self.ax.scatter(right_eye[0], right_eye[1], right_eye[2], 
                               color='blue', s=50, label='Right Eye')
                
                # Plot gaze vectors
                gaze_length = 300  # mm
                self.ax.quiver(left_eye[0], left_eye[1], left_eye[2],
                              left_gaze[0], left_gaze[1], left_gaze[2],
                              color='green', length=gaze_length, label='Left Gaze')
                self.ax.quiver(right_eye[0], right_eye[1], right_eye[2],
                              right_gaze[0], right_gaze[1], right_gaze[2],
                              color='blue', length=gaze_length, label='Right Gaze')
            
            # Plot selected face landmarks
            face_x = [landmark[0] for landmark in face_landmarks_3d]
            face_y = [landmark[1] for landmark in face_landmarks_3d]
            face_z = [landmark[2] for landmark in face_landmarks_3d]
            
            self.ax.scatter(face_x, face_y, face_z, color='black', s=1, alpha=0.3, label='Face')
        
        # Set axis limits with some padding
        x_min, x_max = min(-350, min(tv_x)), max(350, max(tv_x))
        y_min, y_max = min(0, min(tv_y)), max(400, max(tv_y))
        z_min, z_max = -400, 400
        
        self.ax.set_xlim(x_min, x_max)
        self.ax.set_ylim(y_min, y_max)
        self.ax.set_zlim(z_min, z_max)
        
        # Add legend
        self.ax.legend()
        
        # Update plot
        self.fig.canvas.draw_idle()
        self.fig.canvas.flush_events()
    
    def run(self):
        """Main run loop"""
        # Start streaming
        profile = self.pipeline.start(self.config)
        
        # Get camera intrinsics from the color stream
        stream = profile.get_stream(rs.stream.color)
        intrinsics = stream.as_video_stream_profile().get_intrinsics()
        
        # Create align object
        align = rs.align(rs.stream.color)
        
        # Set up matplotlib for interactive plotting
        plt.ion()  # Enable interactive mode
        self.fig = plt.figure(figsize=(10, 8))
        self.ax = self.fig.add_subplot(111, projection='3d')
        
        # Get monitor information
        monitors = get_monitors()
        if len(monitors) > 1:
            primary = monitors[0]
            secondary = monitors[1]
            print(f"Detected monitors: Primary ({primary.width}x{primary.height}) and Secondary ({secondary.width}x{secondary.height})")
            print(f"Target will be displayed on secondary monitor at position ({secondary.x}, {secondary.y})")
            
            # Create a named window for the TV display in fullscreen mode
            cv2.namedWindow("TV Display", cv2.WND_PROP_FULLSCREEN)
            cv2.setWindowProperty("TV Display", cv2.WND_PROP_FULLSCREEN, cv2.WINDOW_FULLSCREEN)
            cv2.moveWindow("TV Display", secondary.x, secondary.y)
        else:
            print("Warning: Only one monitor detected. Target display may not appear correctly.")
            cv2.namedWindow("TV Display", cv2.WINDOW_NORMAL)
        
        # Create a window for the RealSense feed and Gaze visualization
        cv2.namedWindow("RealSense", cv2.WINDOW_NORMAL)
        cv2.namedWindow("Gaze Projection", cv2.WINDOW_NORMAL)
        
        print("Interactive 3D visualization initialized. You can rotate view with mouse.")
        print("Press 'q' to quit.")
        
        try:
            while True:
                # Wait for a coherent pair of frames
                frames = self.pipeline.wait_for_frames()
                
                # Align depth frame to color frame
                aligned_frames = align.process(frames)
                depth_frame = aligned_frames.get_depth_frame()
                color_frame = aligned_frames.get_color_frame()
                
                if not depth_frame or not color_frame:
                    continue
                
                # Convert images to numpy arrays
                depth_image = np.asanyarray(depth_frame.get_data())
                color_image = np.asanyarray(color_frame.get_data())
                
                # Apply colormap on depth image
                depth_colormap = cv2.applyColorMap(
                    cv2.convertScaleAbs(depth_image, alpha=0.03), 
                    cv2.COLORMAP_JET
                )
                
                # Process face landmarks
                face_landmarks_3d = self.process_face_landmarks(color_image, depth_frame, intrinsics)
                
                # Get eye gaze vectors
                left_eye, left_gaze, right_eye, right_gaze = self.get_eye_gaze_vectors(face_landmarks_3d)
                
                # Draw gaze on color image
                gaze_image = self.draw_gaze_on_image(
                    color_image, left_eye, left_gaze, right_eye, right_gaze, intrinsics
                )
                
                # Combine color and depth for display
                combined_image = np.hstack((color_image, depth_colormap))
                
                # Move target
                self.move_target()
                
                # Draw target on TV
                self.draw_target_on_tv()
                
                # Update 3D visualization
                self.update_3d_plot(face_landmarks_3d)
                
                # Show RealSense feed
                cv2.imshow('RealSense', combined_image)
                
                # Show gaze projection visualization
                cv2.imshow('Gaze Projection', gaze_image)
                
                # Process matplotlib events to ensure interactivity
                plt.pause(0.01)
                
                # Exit on 'q' press
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
                
        finally:
            # Stop streaming
            self.pipeline.stop()
            cv2.destroyAllWindows()
            plt.close()

if __name__ == "__main__":
    tracker = TVFaceTracker()
    tracker.run()