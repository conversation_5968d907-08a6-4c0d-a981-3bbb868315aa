import os
import cv2
import numpy as np
import pandas as pd


# 1.Plane Equation to represent TD
def plane_equation_from_manoj():
    top_left = np.array(
        [290, 25, 15]
    )
    top_right = np.array(
        [-330, 25, 15]
    )
    bottom_left = np.array(
        [290, 366, 15]
    )
    # 3 point is enough for hyperplane equation.
    # bottom_right = np.array(
    #     [-330, 366, 15]
    # )

    # Calculate the normalized Normal vector to the plane.
    vec1 = top_right - top_left
    vec2 = bottom_left - top_left
    normal = np.cross(vec1, vec2)
    norm_value = np.linalg.norm(normal)
    if norm_value == 0:
        raise ValueError(
            "Cannot calculate plane equation. Normal vector has zero norm"
        )

    normal = normal / norm_value

    # Extract the coefficients of the plane equation.
    a = normal[0]
    b = normal[1]
    c = normal[2]
    d = -np.dot(normal, top_left)
    return np.array([a, b, c, d])


# 2. Plane Vector Intersection Point
def plane_vector_intersection_from_manoj(plane_w, plane_b,
    gaze_vector, face_center_3d, r_screen_to_cam, t_screen_to_cam,
    resolution, dim):

    tv_plane_a, tv_plane_b, tv_plane_c = plane_w
    tv_plane_d = plane_b
    plane_coeff = np.array(
        [tv_plane_a, tv_plane_b, tv_plane_c, tv_plane_d]            #this is plane equation a,b,c,d from above 
    )
    point1 = face_center_3d        # make it face center 3d

    # Compute the direction vector joining two points.
    direction_vec = gaze_vector
    direction_vec_norm = np.linalg.norm(direction_vec)
    unit_direction_vec = direction_vec / direction_vec_norm             # make it gaze direction vector 

    # Calculate the parameter t at which the vector intersects the plane.
    t = -(np.dot(plane_coeff[:3], point1) + tv_plane_d) / np.dot(
        plane_coeff[:3], unit_direction_vec
    )

    point1 = np.squeeze(point1)  # from (3,1) -> (3,)
    unit_direction_vec = np.squeeze(unit_direction_vec)  # make sure (3,)
    
    # Calculate the point of intersection.
    intersection_point = point1 + t * unit_direction_vec

    # This part is redundant if i directly compute withing tv logic because that is what i need.
    # We return Point3D dataclass object but not numpy array.
    ipoint_gcc = (
        intersection_point[0], intersection_point[1], intersection_point[2]
    )

    ix, iy, iz = intersection_point.reshape(3)
    
    is_within_tv = (
    (ix < 290) and (ix > -330) and (iy > 25) and (iy < 366)
    )

    if is_within_tv:
        # make 4D
        ipoint_hom = np.array([intersection_point[0], intersection_point[1], intersection_point[2], 1])
        ipoint_gcc = np.expand_dims(ipoint_hom, axis=-1)

        R_cam2scr = r_screen_to_cam.T
        t_screen_to_cam = np.vstack([t_screen_to_cam, np.array([[1]])])

        # Only use (x, y, z) for rotation
        # relative_point = ipoint_gcc[:3, :] - t_screen_to_cam[:3, :]
        # ipoint_tdc = R_cam2scr @ relative_point
        
        relative_point = R_cam2scr @ ipoint_gcc[:3,:]
        ipoint_tdc = relative_point + (-R_cam2scr @ t_screen_to_cam[:3,:])
        ipoint_tdc = np.squeeze(ipoint_tdc, axis=-1)
        
        # Continue with u,v calculation
        u = int(ipoint_tdc[0] * (resolution[1] / dim[1]))
        v = int(ipoint_tdc[1] * (resolution[0] / dim[0]))

        return is_within_tv, (u, v), ipoint_tdc
    
    else:
        return is_within_tv, None, None
    

def gaze_vector_to_screen_pixel(gaze_vector_normalized):
    face_center = np.array([-19.52928252, -15.81111481, 685.75523013])
    rotation_matrix = np.array([[0.99959381, -0.00216748,  0.02841696 ],
                                [0.00151127,  0.99973217,  0.02309336],
                                [-0.0284594,  -0.02304104,  0.99932936]])
    r_screen_to_cam = np.array([[-1.0, 0.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, -1.0]])
    t_screen_to_cam = np.array([[290], [25], [15]])
    monitor_mm = (620, 350)
    monitor_pixels = (3840, 2160)
    plane = plane_equation_from_manoj()
    plane_w = plane[0:3]
    plane_b = plane[3]
    gaze_vector = np.dot(rotation_matrix.T, gaze_vector_normalized)   # In camera space
    
    _, intersection_point, _ = plane_vector_intersection_from_manoj(plane_w, plane_b, gaze_vector, face_center, r_screen_to_cam, t_screen_to_cam, monitor_pixels, monitor_mm)
    print("Intersection Point:", intersection_point)


def draw_gaze_arrow(img, pitch, yaw, length=50, color=(0, 0, 255), thickness=2):
    """
    Draw gaze arrow using correct interpretation of pitch/yaw.
    - Positive yaw → right
    - Positive pitch → down
    """
    h, w = img.shape[:2]
    center = (w // 2, h // 2)

    # Convert pitch/yaw back into 3D direction (unit vector)
    x = -np.cos(pitch) * np.sin(yaw)
    y = -np.sin(pitch)
    z = np.cos(yaw) * np.cos(pitch)
    
    # Project to 2D (image plane), ignoring Z
    dx = x * length
    dy = y * length

    end_point = (int(center[0] + dx), int(center[1] + dy))
    cv2.arrowedLine(img, center, end_point, color, thickness, tipLength=0.3)
    return img, (x, y, z)


def main(csv_path, base_path):
    df = pd.read_csv(csv_path)

    for _, row in df.iterrows():
        face_img_path = os.path.join(base_path, row['face_file_name'])
        pitch = float(row['pitch'])
        yaw = float(row['yaw'])

        img = cv2.imread(face_img_path)
        if img is None:
            print(f"Failed to load image: {face_img_path}")
            continue

        _, gaze_vector = draw_gaze_arrow(img, pitch, yaw)
        gaze_vector_to_screen_pixel(gaze_vector)


if __name__ == '__main__':
    # Example usage
    root = "/home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Gaze-Training-Pipeline"
    base_path = "datasets/DeltaX/dataset_normalized/test"
    
    csv_path = f"{root}/{base_path}/labels.csv"
    main(csv_path, base_path)

'''
python assets/backup/scripts/gaze_vector_to_screen_pixel.py
'''
