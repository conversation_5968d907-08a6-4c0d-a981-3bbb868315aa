import numpy as np


def plane_equation(rmat: np.n<PERSON><PERSON>, tmat: np.n<PERSON><PERSON>) -> np.ndarray:
    """
    Computes the plane equation ax + by + cz = d from rotation and translation.
    Normal vector is z-axis from rotation matrix. tmat is a point on the plane.

    :param rmat: Rotation matrix (3x3)
    :param tmat: Translation vector (3x1)
    :return: Plane coefficients [a, b, c, d]
    """
    assert rmat.shape == (3, 3), "rmat must be 3x3"
    assert tmat.size == 3, "tmat must have 3 elements"

    normal = rmat[:, 2]
    point = tmat.flatten()

    print(normal, point)
    
    a, b, c = normal
    d = np.dot(normal, point)

    return np.array([a, b, c, d])


def compute_r_t_from_plane(tv_corners: dict):
    """
    Computes the rotation matrix R and translation vector t from screen corners.

    :param tv_corners: Dictionary with 3D coordinates of screen corners
    :return: (R, t) where R is 3x3 rotation matrix and t is 3x1 translation vector
    """
    top_left = tv_corners["top_left"]
    top_right = tv_corners["top_right"]
    bottom_left = tv_corners["bottom_left"]

    # X and Y axes along the plane
    x_dir = top_right - top_left
    y_dir = bottom_left - top_left

    x_axis = x_dir / np.linalg.norm(x_dir)
    y_axis = y_dir / np.linalg.norm(y_dir)

    # Z axis is the normal to the plane
    z_axis = np.cross(x_axis, y_axis)
    z_axis /= np.linalg.norm(z_axis)

    # Create rotation matrix: columns are x, y, z axes
    R = np.column_stack((x_axis, y_axis, z_axis))
    t = top_left.reshape(3, 1)

    return R, t


if __name__ == "__main__":
    # Screen corners in camera coordinates (x, y, z) in mm
    tv_corners = {
        "top_left": np.array([290, 25, 15]),
        "top_right": np.array([-330, 25, 15]),
        "bottom_right": np.array([-330, 366, 15]),
        "bottom_left": np.array([290, 366, 15]),
    }

    # Compute R and t
    R, t = compute_r_t_from_plane(tv_corners)

    print("Rotation matrix R:\n", R)
    print("Translation vector t:\n", t)
    
    # Compute plane equation
    plane = plane_equation(R, t)
    print("Plane equation coefficients (a, b, c, d):", plane)

    # Optional: human-readable plane equation
    print(f"The plane equation is: {plane[0]:.4f}x + {plane[1]:.4f}y + {plane[2]:.4f}z = {plane[3]:.4f}")
