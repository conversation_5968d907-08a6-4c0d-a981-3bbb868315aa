import cv2
import numpy as np
import pandas as pd
import ast
import pathlib
import mediapipe as mp
from argparse import ArgumentParser

from dataset_utils import get_camera_matrix, get_face_landmarks_in_ccs_from_depth, compute_rvec_from_landmarks
from mpii_face_gaze_preprocessing import normalize_single_image


def pixel_to_camera_point(point_px, monitor_pixels, monitor_mm, rvec, tvec_cam2scr_mm):
    px, py          = point_px
    W_px, H_px      = monitor_pixels
    W_mm, H_mm      = monitor_mm

    # 1) pixel→mm in screen frame
    x_mm =       px * (W_mm / W_px)
    y_mm =       py * (H_mm / H_px)

    # screen_pt should be (3, 1)
    screen_pt = np.array([[x_mm], [y_mm], [0.0]], dtype=np.float32)

    # Step 2: Hardcoded screen → camera transformation
    R_scr2cam = np.array([[-1.,  0.,  0.],
                          [ 0.,  1.,  0.],
                          [ 0.,  0., -1.]], dtype=np.float32)
    tvec_scr2cam_mm = np.array([[290.0], [25.0], [15.0]], dtype=np.float32)  # already in mm

    # Step 3: Apply screen → camera transform directly
    pt_cam = R_scr2cam @ screen_pt + tvec_scr2cam_mm

    return pt_cam  # shape (3,1)



def main(raw_base: str, normalized_base: str, intrinsics_path: str):
    """
    Process raw gaze data CSV to produce normalized face/eye crops and labels CSV.
    """
    raw_base = pathlib.Path(raw_base)
    norm_base = pathlib.Path(normalized_base)
    norm_base.mkdir(parents=True, exist_ok=True)

    # load camera intrinsics (unused here but can be passed to normalize)
    camera_matrix, dist_coefficients = get_camera_matrix(intrinsics_path)
    
    # # setup MediaPipe for static images
    # face_mesh = mp.solutions.face_mesh.FaceMesh(static_image_mode=True,
    #                                            max_num_faces=1,
    #                                            refine_landmarks=True)

    # read raw CSV
    df = pd.read_csv(raw_base / 'data.csv')
    labels = []  # collect rows for labels.csv

    for _, row in df.iterrows():
        # parse fields
        session, fname = row['file_name'].split('/', 1)
        basename = pathlib.Path(fname).stem  # without extension
        # ensure session folder exists
        session_folder = norm_base / session
        session_folder.mkdir(exist_ok=True)

        # load image and depth
        img_path = raw_base / row['file_name']
        img = cv2.imread(str(img_path))
        depth_path = img_path.parent / f"{img_path.stem}_depth.npy"
        if not depth_path.exists():
            print(f"Depth file not found: {depth_path}, skipping.")
            continue
        depth      = np.load(str(depth_path))

        # parse metadata
        px, py = ast.literal_eval(row['point_on_screen'])
        monitor_mm = ast.literal_eval(row['monitor_mm'])
        monitor_pixels = ast.literal_eval(row['monitor_pixels'])
        rvec = np.asarray(ast.literal_eval(row['rvec']), dtype=float)
        tvec_mm = np.asarray(ast.literal_eval(row['tvec']), dtype=float)
        tvec_mm

        # compute 3D gaze target
        gaze_target_3d = pixel_to_camera_point((px, py), monitor_pixels, monitor_mm, rvec, tvec_mm)

        # detect 2D landmarks using mediapipe
        # img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        # results = face_mesh.process(img_rgb)
        # if not results.multi_face_landmarks:
        #     print(f"No face detected in {img_path}, skipping.")
        #     continue
        # h, w, _ = img.shape
        # lm2d = np.array([[lm.x * w, lm.y * h]
        #                  for lm in results.multi_face_landmarks[0].landmark])
        
        
        # Load saved 3D face landmarks
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        landmark_path = img_path.parent / f"{img_path.stem}_landmarks.npy"
        if not landmark_path.exists():
            print(f"Landmark file not found: {landmark_path}, skipping.")
            continue

        landmarks_norm = np.load(str(landmark_path))  # saved as x, y, z
        h, w, _ = img.shape
        lm2d = np.array([[x * w, y * h] for x, y, _ in landmarks_norm])
        
        landmarks_3d = get_face_landmarks_in_ccs_from_depth(camera_matrix, depth, lm2d)

        # compute centers
        left_eye_center = landmarks_3d[:, 468].reshape((3, 1))  
        right_eye_center = landmarks_3d[:, 473].reshape((3, 1))
        face_center = landmarks_3d.mean(axis=1).reshape((3, 1))

        # compute head pose from landmarks
        rvec_head, _ = compute_rvec_from_landmarks(landmarks_3d)


        # normalize crops
        face_img, gaze_norm, normalized_rotation_matrix = normalize_single_image(img_rgb, rvec_head, gaze_target_3d, face_center, camera_matrix, is_eye=False)
        left_img, _, _       = normalize_single_image(img_rgb, rvec_head, None, left_eye_center, camera_matrix)
        right_img, _, _      = normalize_single_image(img_rgb, rvec_head, None, right_eye_center, camera_matrix)
        
        # compute pitch, yaw (Radian)
        pitch = np.arcsin(-gaze_norm[1])
        
        # Original code: yaw = np.arctan2(-gaze_norm[0], -gaze_norm[2])
        yaw = -np.arctan2(gaze_norm[0], gaze_norm[2])

        # save images
        face_file  = session_folder / f"{basename}-face.jpg"
        left_file  = session_folder / f"{basename}-left-eye.jpg"
        right_file = session_folder / f"{basename}-right-eye.jpg"
        cv2.imwrite(str(face_file), cv2.cvtColor(face_img, cv2.COLOR_RGB2BGR))
        cv2.imwrite(str(left_file), cv2.cvtColor(left_img, cv2.COLOR_RGB2BGR))
        cv2.imwrite(str(right_file), cv2.cvtColor(right_img, cv2.COLOR_RGB2BGR))

        labels.append({
            'face_file_name':  f"{session}/{face_file.name}",
            'left_eye':        f"{session}/{left_file.name}",
            'right_eye':       f"{session}/{right_file.name}",
            'pitch':           pitch,
            'yaw':             yaw
        })

    # write labels CSV
    labels_df = pd.DataFrame(labels)
    labels_df.to_csv(norm_base / 'labels.csv', index=False)
    print(f"Saved {len(labels)} entries to {norm_base / 'labels.csv'}")


if __name__ == '__main__':
    parser = ArgumentParser()
    parser.add_argument('--raw_base', type=str, default='./data', help='Root of raw data with data.csv and pXX folders')
    parser.add_argument('--normalized_base', type=str, default='./normalized_custom_data', help='Output root for normalized crops and labels.csv')
    parser.add_argument('--intrinsics_path', type=str, default='./calibration_matrix.yaml', help='Camera intrinsics YAML')
    args = parser.parse_args()

    main(args.raw_base, args.normalized_base, args.intrinsics_path)
    

'''
python custom_gaze_data_preprocessing.py --raw_base=../custom_data/temp_data --normalized_base=../custom_data/normalized_temp_data \
    --intrinsics_path=../custom_data/depth_camera_calibration.yaml

'''