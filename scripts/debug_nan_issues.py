#!/usr/bin/env python3
"""
Debug script for identifying and fixing NaN issues in gaze tracking training.

This script helps diagnose common causes of NaN losses during training:
1. Data quality issues (NaN/Inf in inputs)
2. Model parameter issues
3. Loss function problems
4. Gradient explosion

Usage:
    python scripts/debug_nan_issues.py
"""

import torch
import torch.nn.functional as F
import numpy as np
from pathlib import Path
import sys
import matplotlib.pyplot as plt

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from utils.config import get_config
from utils.gaze_dataset import get_dataloaders
from nets.nn import FinalModel
from utils.util import angular_loss, calc_angle_error


def check_data_quality(dataloader, max_batches=5):
    """
    Check data quality for NaN/Inf values and extreme ranges.
    
    Args:
        dataloader: PyTorch DataLoader to check
        max_batches: Maximum number of batches to check
    """
    print("🔍 Checking data quality...")
    
    issues_found = []
    
    for batch_idx, batch in enumerate(dataloader):
        if batch_idx >= max_batches:
            break
            
        print(f"  Batch {batch_idx + 1}/{max_batches}")
        
        # Check each tensor in the batch
        for key, value in batch.items():
            if torch.is_tensor(value):
                # Check for NaN
                if torch.isnan(value).any():
                    issues_found.append(f"NaN found in {key} at batch {batch_idx}")
                    
                # Check for Inf
                if torch.isinf(value).any():
                    issues_found.append(f"Inf found in {key} at batch {batch_idx}")
                    
                # Check for extreme values
                if value.dtype in [torch.float32, torch.float64]:
                    min_val, max_val = value.min().item(), value.max().item()
                    if abs(min_val) > 1e6 or abs(max_val) > 1e6:
                        issues_found.append(f"Extreme values in {key}: [{min_val:.2e}, {max_val:.2e}]")
                        
                    print(f"    {key}: shape={value.shape}, range=[{min_val:.4f}, {max_val:.4f}]")
    
    if issues_found:
        print("❌ Data quality issues found:")
        for issue in issues_found:
            print(f"    - {issue}")
        return False
    else:
        print("✅ Data quality check passed!")
        return True


def check_model_parameters(model):
    """
    Check model parameters for NaN/Inf values and extreme gradients.
    
    Args:
        model: PyTorch model to check
    """
    print("🔍 Checking model parameters...")
    
    issues_found = []
    
    for name, param in model.named_parameters():
        if param is not None:
            # Check parameter values
            if torch.isnan(param).any():
                issues_found.append(f"NaN in parameter {name}")
            if torch.isinf(param).any():
                issues_found.append(f"Inf in parameter {name}")
                
            # Check gradients if they exist
            if param.grad is not None:
                if torch.isnan(param.grad).any():
                    issues_found.append(f"NaN in gradient of {name}")
                if torch.isinf(param.grad).any():
                    issues_found.append(f"Inf in gradient of {name}")
                    
                grad_norm = param.grad.norm().item()
                if grad_norm > 100:
                    issues_found.append(f"Large gradient in {name}: {grad_norm:.2e}")
            
            param_norm = param.norm().item()
            print(f"    {name}: param_norm={param_norm:.4f}")
    
    if issues_found:
        print("❌ Model parameter issues found:")
        for issue in issues_found:
            print(f"    - {issue}")
        return False
    else:
        print("✅ Model parameter check passed!")
        return True


def test_loss_functions(model, dataloader):
    """
    Test loss functions with sample data to identify issues.
    
    Args:
        model: PyTorch model
        dataloader: DataLoader for testing
    """
    print("🔍 Testing loss functions...")
    
    model.eval()
    batch = next(iter(dataloader))
    
    # Move to device
    device = next(model.parameters()).device
    for key, value in batch.items():
        if torch.is_tensor(value):
            batch[key] = value.to(device)
    
    with torch.no_grad():
        # Forward pass
        outputs = model(
            batch['person_idx'],
            batch['full_face_image'],
            batch['right_eye_image'],
            batch['left_eye_image']
        )
        
        labels = torch.stack([batch['gaze_pitch'], batch['gaze_yaw']], dim=1)
        
        print(f"    Labels range: [{labels.min().item():.4f}, {labels.max().item():.4f}]")
        print(f"    Outputs range: [{outputs.min().item():.4f}, {outputs.max().item():.4f}]")
        
        # Test L1 loss
        try:
            l1_loss = F.l1_loss(outputs, labels)
            print(f"    L1 loss: {l1_loss.item():.6f}")
            if torch.isnan(l1_loss) or torch.isinf(l1_loss):
                print("    ❌ L1 loss is invalid!")
        except Exception as e:
            print(f"    ❌ L1 loss failed: {e}")
        
        # Test angular loss
        try:
            ang_loss = angular_loss(labels, outputs)
            print(f"    Angular loss: {ang_loss.item():.6f}")
            if torch.isnan(ang_loss) or torch.isinf(ang_loss):
                print("    ❌ Angular loss is invalid!")
        except Exception as e:
            print(f"    ❌ Angular loss failed: {e}")
        
        # Test angle error
        try:
            angle_error = calc_angle_error(labels, outputs)
            print(f"    Angle error: {angle_error.item():.2f}°")
        except Exception as e:
            print(f"    ❌ Angle error failed: {e}")


def test_extreme_inputs(model):
    """
    Test model with extreme inputs to check robustness.
    
    Args:
        model: PyTorch model to test
    """
    print("🔍 Testing with extreme inputs...")
    
    device = next(model.parameters()).device
    batch_size = 2
    
    # Test with extreme angle values
    extreme_angles = torch.tensor([
        [-torch.pi, torch.pi],  # Maximum negative/positive
        [torch.pi, -torch.pi],  # Swapped
    ], device=device)
    
    # Create dummy inputs
    person_idx = torch.zeros(batch_size, dtype=torch.long, device=device)
    face_img = torch.randn(batch_size, 3, 96, 96, device=device)
    right_eye = torch.randn(batch_size, 3, 64, 96, device=device)
    left_eye = torch.randn(batch_size, 3, 64, 96, device=device)
    
    model.eval()
    with torch.no_grad():
        try:
            outputs = model(person_idx, face_img, right_eye, left_eye)
            print(f"    Model outputs with extreme inputs: {outputs}")
            
            # Test loss with extreme values
            ang_loss = angular_loss(extreme_angles, outputs)
            print(f"    Angular loss with extreme inputs: {ang_loss.item():.6f}")
            
        except Exception as e:
            print(f"    ❌ Model failed with extreme inputs: {e}")


def main():
    """Main debugging function."""
    print("🚀 Starting NaN debugging session...")
    
    # Load configuration
    config = get_config()
    training_config = config['training']
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Load data
    print("\n📊 Loading data...")
    try:
        train_loader, val_loader, test_loader = get_dataloaders(
            data_root=training_config['dataset_path'],
            batch_size=training_config['batch_size'],
            val_split=training_config['val_split'],
            augmentation_config=config.get('augmentation', {})
        )
        print("✅ Data loaded successfully!")
    except Exception as e:
        print(f"❌ Failed to load data: {e}")
        return
    
    # Check data quality
    print("\n" + "="*50)
    data_ok = check_data_quality(train_loader, max_batches=3)
    
    # Load model
    print("\n🤖 Loading model...")
    try:
        model = FinalModel()
        model.to(device)
        print("✅ Model loaded successfully!")
    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        return
    
    # Check model parameters
    print("\n" + "="*50)
    model_ok = check_model_parameters(model)
    
    # Test loss functions
    print("\n" + "="*50)
    test_loss_functions(model, train_loader)
    
    # Test extreme inputs
    print("\n" + "="*50)
    test_extreme_inputs(model)
    
    # Summary
    print("\n" + "="*50)
    print("📋 DEBUGGING SUMMARY:")
    print(f"  Data quality: {'✅ PASS' if data_ok else '❌ FAIL'}")
    print(f"  Model parameters: {'✅ PASS' if model_ok else '❌ FAIL'}")
    
    if data_ok and model_ok:
        print("\n🎉 No obvious issues found! Your training should be more stable now.")
    else:
        print("\n⚠️  Issues detected. Please review the output above and fix the problems.")
    
    print("\n💡 RECOMMENDATIONS:")
    print("  1. Use the improved angular_loss function with numerical stability")
    print("  2. Enable gradient clipping (gradient_clip_val=1.0)")
    print("  3. Use a lower learning rate (1e-5 instead of 5e-5)")
    print("  4. Monitor training with detect_anomaly=True")
    print("  5. Use full precision (precision='32-true')")


if __name__ == "__main__":
    main()
