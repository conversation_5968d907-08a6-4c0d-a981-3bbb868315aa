"""
Data Distribution Analysis Script

This script analyzes the distribution of gaze data in both raw and normalized formats.
It generates visualizations and statistics to help understand:
1. The distribution of pitch and yaw angles
2. The distribution of 3D gaze vectors
3. The coverage of gaze directions
4. Comparison between raw and normalized data

Usage:
    python scripts/data_preprocessing/show_data_distribution.py \
        --dataset_path=datasets/DeltaX/dataset_normalized \
        --output_path=outputs/data_distribution_results
"""
import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from mpl_toolkits.mplot3d import Axes3D  # Required for 3D plotting
from matplotlib.gridspec import GridSpec

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.config import get_config
from utils.util import pitchyaw_to_gaze_vector


def compute_statistics(data: np.ndarray, name: str) -> dict:
    """
    Compute comprehensive statistical measures for a dataset.

    Args:
        data: Array of numerical data
        name: Descriptive name for the dataset

    Returns:
        Dictionary containing statistical measures including mean, median, std, percentiles, etc.
    """
    stats = {
        'name': name,
        'count': len(data),
        'mean': np.mean(data),
        'median': np.median(data),
        'std': np.std(data),
        'min': np.min(data),
        'max': np.max(data),
        'range': np.max(data) - np.min(data),
        '25%': np.percentile(data, 25),
        '75%': np.percentile(data, 75),
    }
    return stats


def print_statistics(stats: dict) -> None:
    """
    Print statistical measures in a well-formatted, readable way.

    Args:
        stats: Dictionary containing statistical measures from compute_statistics()
    """
    print(f"\n{stats['name']} Statistics:")
    print(f"  Count: {stats['count']}")
    print(f"  Mean: {stats['mean']:.4f}°")
    print(f"  Median: {stats['median']:.4f}°")
    print(f"  Std Dev: {stats['std']:.4f}°")
    print(f"  Min: {stats['min']:.4f}°")
    print(f"  Max: {stats['max']:.4f}°")
    print(f"  Range: {stats['range']:.4f}°")
    print(f"  25th Percentile: {stats['25%']:.4f}°")
    print(f"  75th Percentile: {stats['75%']:.4f}°")


def plot_angle_distributions(raw_pitch, raw_yaw, norm_pitch, norm_yaw, output_path):
    """Plot distributions of pitch and yaw angles."""
    # Create figure with 2x2 subplots
    fig = plt.figure(figsize=(15, 12))
    gs = GridSpec(2, 2, figure=fig)

    # Plot histograms
    ax1 = fig.add_subplot(gs[0, 0])
    ax1.hist(raw_pitch, bins=30, alpha=0.7, label='Raw')
    ax1.hist(norm_pitch, bins=30, alpha=0.7, label='Normalized')
    ax1.set_xlabel('Pitch (degrees)')
    ax1.set_ylabel('Frequency')
    ax1.set_title('Pitch Angle Distribution')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    ax2 = fig.add_subplot(gs[0, 1])
    ax2.hist(raw_yaw, bins=30, alpha=0.7, label='Raw')
    ax2.hist(norm_yaw, bins=30, alpha=0.7, label='Normalized')
    ax2.set_xlabel('Yaw (degrees)')
    ax2.set_ylabel('Frequency')
    ax2.set_title('Yaw Angle Distribution')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # Plot KDE (Kernel Density Estimation)
    ax3 = fig.add_subplot(gs[1, 0])
    sns.kdeplot(raw_pitch, label='Raw', ax=ax3)
    sns.kdeplot(norm_pitch, label='Normalized', ax=ax3)
    ax3.set_xlabel('Pitch (degrees)')
    ax3.set_ylabel('Density')
    ax3.set_title('Pitch Angle Density')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    ax4 = fig.add_subplot(gs[1, 1])
    sns.kdeplot(raw_yaw, label='Raw', ax=ax4)
    sns.kdeplot(norm_yaw, label='Normalized', ax=ax4)
    ax4.set_xlabel('Yaw (degrees)')
    ax4.set_ylabel('Density')
    ax4.set_title('Yaw Angle Density')
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(output_path, 'angle_distributions.png'))
    plt.close()


def plot_2d_angle_scatter(raw_pitch, raw_yaw, norm_pitch, norm_yaw, output_path):
    """Plot 2D scatter plots of pitch vs yaw angles."""
    # Create figure with 1x2 subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # Raw data scatter plot
    scatter1 = ax1.scatter(raw_yaw, raw_pitch, alpha=0.5, c=raw_pitch,
                          cmap='viridis', s=10)
    ax1.set_xlabel('Yaw (degrees)')
    ax1.set_ylabel('Pitch (degrees)')
    ax1.set_title('Raw Gaze Angles')
    ax1.grid(True, alpha=0.3)
    fig.colorbar(scatter1, ax=ax1, label='Pitch (degrees)')

    # Normalized data scatter plot
    scatter2 = ax2.scatter(norm_yaw, norm_pitch, alpha=0.5, c=norm_pitch,
                          cmap='viridis', s=10)
    ax2.set_xlabel('Yaw (degrees)')
    ax2.set_ylabel('Pitch (degrees)')
    ax2.set_title('Normalized Gaze Angles')
    ax2.grid(True, alpha=0.3)
    fig.colorbar(scatter2, ax=ax2, label='Pitch (degrees)')

    plt.tight_layout()
    plt.savefig(os.path.join(output_path, 'angle_scatter.png'))
    plt.close()


def plot_heatmaps(raw_pitch, raw_yaw, norm_pitch, norm_yaw, output_path):
    """Create 2D heatmaps of gaze angle distributions."""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # Raw data heatmap
    heatmap1 = ax1.hexbin(raw_yaw, raw_pitch, gridsize=30, cmap='viridis')
    ax1.set_xlabel('Yaw (degrees)')
    ax1.set_ylabel('Pitch (degrees)')
    ax1.set_title('Raw Gaze Angle Density')
    fig.colorbar(heatmap1, ax=ax1, label='Count')

    # Normalized data heatmap
    heatmap2 = ax2.hexbin(norm_yaw, norm_pitch, gridsize=30, cmap='viridis')
    ax2.set_xlabel('Yaw (degrees)')
    ax2.set_ylabel('Pitch (degrees)')
    ax2.set_title('Normalized Gaze Angle Density')
    fig.colorbar(heatmap2, ax=ax2, label='Count')

    plt.tight_layout()
    plt.savefig(os.path.join(output_path, 'gaze_heatmaps.png'))
    plt.close()


def plot_3d_gaze_vectors(raw_vectors, norm_vectors, output_path):
    """Plot 3D gaze vectors on a unit sphere."""
    fig = plt.figure(figsize=(15, 12))
    gs = GridSpec(2, 2, figure=fig)

    # 3D scatter plot of raw vectors
    ax1 = fig.add_subplot(gs[0, 0], projection='3d')
    ax1.scatter(raw_vectors[:, 0], raw_vectors[:, 1], raw_vectors[:, 2],
               alpha=0.5, c=raw_vectors[:, 1], cmap='viridis', s=10)
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.set_zlabel('Z')
    ax1.set_title('Raw Gaze Vectors')

    # 3D scatter plot of normalized vectors
    ax2 = fig.add_subplot(gs[0, 1], projection='3d')
    ax2.scatter(norm_vectors[:, 0], norm_vectors[:, 1], norm_vectors[:, 2],
               alpha=0.5, c=norm_vectors[:, 1], cmap='viridis', s=10)
    ax2.set_xlabel('X')
    ax2.set_ylabel('Y')
    ax2.set_zlabel('Z')
    ax2.set_title('Normalized Gaze Vectors')

    # Plot unit sphere wireframe
    u = np.linspace(0, 2 * np.pi, 20)
    v = np.linspace(0, np.pi, 20)
    x = np.outer(np.cos(u), np.sin(v))
    y = np.outer(np.sin(u), np.sin(v))
    z = np.outer(np.ones(np.size(u)), np.cos(v))

    ax1.plot_wireframe(x, y, z, color='gray', alpha=0.2)
    ax2.plot_wireframe(x, y, z, color='gray', alpha=0.2)

    # Set equal aspect ratio
    ax1.set_box_aspect([1, 1, 1])
    ax2.set_box_aspect([1, 1, 1])

    # 2D projection plots (X-Y plane)
    ax3 = fig.add_subplot(gs[1, 0])
    ax3.scatter(raw_vectors[:, 0], raw_vectors[:, 1],
               alpha=0.5, c=raw_vectors[:, 2], cmap='viridis', s=10)
    ax3.set_xlabel('X')
    ax3.set_ylabel('Y')
    ax3.set_title('Raw Gaze Vectors (X-Y Projection)')
    ax3.grid(True, alpha=0.3)
    # Draw unit circle
    circle = plt.Circle((0, 0), 1, fill=False, color='gray', linestyle='--')
    ax3.add_artist(circle)
    ax3.set_aspect('equal')

    ax4 = fig.add_subplot(gs[1, 1])
    ax4.scatter(norm_vectors[:, 0], norm_vectors[:, 1],
               alpha=0.5, c=norm_vectors[:, 2], cmap='viridis', s=10)
    ax4.set_xlabel('X')
    ax4.set_ylabel('Y')
    ax4.set_title('Normalized Gaze Vectors (X-Y Projection)')
    ax4.grid(True, alpha=0.3)
    # Draw unit circle
    circle = plt.Circle((0, 0), 1, fill=False, color='gray', linestyle='--')
    ax4.add_artist(circle)
    ax4.set_aspect('equal')

    plt.tight_layout()
    plt.savefig(os.path.join(output_path, 'gaze_vectors_3d.png'))
    plt.close()


def load_gaze_labels(subset_dataset_path: Path) -> dict:
    """
    Load and extract gaze data from normalized dataset for analysis.

    This function reads the labels.csv file from a normalized dataset and extracts
    both raw and normalized gaze information for statistical analysis and visualization.

    Args:
        subset_dataset_path: Path to dataset subset directory containing labels.csv

    Returns:
        Dictionary containing:
        - raw_pitch, raw_yaw: Original gaze angles in radians
        - raw_vectors: Original 3D gaze vectors
        - norm_pitch, norm_yaw: Normalized gaze angles in radians
        - norm_vectors: Normalized 3D gaze vectors
    """
    # Load raw and normalized data
    df = pd.read_csv(subset_dataset_path / 'labels.csv')

    # Extract pitch and yaw from normalized data
    norm_pitch = df['pitch'].values
    norm_yaw = df['yaw'].values

    # Convert normalized pitch/yaw to 3D vectors
    norm_vectors = np.array([pitchyaw_to_gaze_vector(p, y) for p, y in zip(norm_pitch, norm_yaw)])

    # Extract raw gaze vectors from the dataframe
    x = df['gaze_vector_x'].values
    y = df['gaze_vector_y'].values
    z = df['gaze_vector_z'].values
    raw_vectors = np.stack([x, y, z], axis=1)

    # Initialize lists to store pitch and yaw
    raw_pitch, raw_yaw = [], []
    for raw_vector in raw_vectors:
        pitch = np.arcsin(-raw_vector[1])
        yaw = np.arctan2(-raw_vector[0], -raw_vector[2])

        raw_pitch.append(pitch)
        raw_yaw.append(yaw)

    return {
        'raw_pitch': np.array(raw_pitch),
        'raw_yaw': np.array(raw_yaw),
        'raw_vectors': raw_vectors,
        'norm_pitch': norm_pitch,
        'norm_yaw': norm_yaw,
        'norm_vectors': norm_vectors
    }


def analyze_data_distribution(subset_dataset_path, subset_output_path):
    """Main function to analyze data distributions."""
    _set = subset_dataset_path.name

    # Create output directory
    subset_output_path.mkdir(parents=True, exist_ok=True)

    # Extract data pitch and yaw from raw data (this needs to be adapted to your data format)
    data = load_gaze_labels(subset_dataset_path)

    # Convert to degrees for better readability
    raw_pitch = np.degrees(data["raw_pitch"])
    raw_yaw = np.degrees(data["raw_yaw"])
    norm_pitch = np.degrees(data["norm_pitch"])
    norm_yaw = np.degrees(data["norm_yaw"])
    raw_vectors = data["raw_vectors"]
    norm_vectors = data["norm_vectors"]

    # Compute statistics
    raw_pitch_stats = compute_statistics(raw_pitch, "Raw Pitch")
    raw_yaw_stats = compute_statistics(raw_yaw, "Raw Yaw")
    norm_pitch_stats = compute_statistics(norm_pitch, "Normalized Pitch")
    norm_yaw_stats = compute_statistics(norm_yaw, "Normalized Yaw")

    # Print statistics
    print(f"\n{'='*10} Statistics for {_set}-set {'='*10}")
    print_statistics(raw_pitch_stats)
    print_statistics(raw_yaw_stats)
    print_statistics(norm_pitch_stats)
    print_statistics(norm_yaw_stats)
    print(f"\n{'='*45}")

    # Generate plots
    print(f"\nGenerating plots for {_set} ...")

    # Plot angle distributions
    plot_angle_distributions(raw_pitch, raw_yaw, norm_pitch, norm_yaw, subset_output_path)
    print("  - Angle distributions plot saved")

    # Plot 2D scatter plots
    plot_2d_angle_scatter(raw_pitch, raw_yaw, norm_pitch, norm_yaw, subset_output_path)
    print("  - Angle scatter plot saved")

    # Plot 3D gaze vectors
    plot_3d_gaze_vectors(raw_vectors, norm_vectors, subset_output_path)
    print("  - 3D gaze vectors plot saved")

    # Plot heatmaps
    plot_heatmaps(raw_pitch, raw_yaw, norm_pitch, norm_yaw, subset_output_path)
    print("  - Gaze heatmaps saved")


def main():
    # Load configuration from file and update with command-line arguments
    config = get_config()

    # Convert paths to Path objects for easier handling
    dataset_path = Path(config['data_distribution']['dataset_path'])
    output_path = Path(config['data_distribution']['output_path'])

    print(f"\nLoading data from: {dataset_path}")
    print(f"Saving results to: {output_path}")

    for _set in ['train', 'test']:
        subset_dataset_path = dataset_path / _set
        subset_output_path = output_path / _set

        analyze_data_distribution(subset_dataset_path, subset_output_path)

    print("\nAnalysis complete!")
    print(f"All results saved to: {output_path}")


if __name__ == "__main__":
    main()
