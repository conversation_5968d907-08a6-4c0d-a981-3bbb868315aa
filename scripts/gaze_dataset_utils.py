from enum import Enum
from typing import <PERSON>ple

import cv2
import yaml
import numpy as np

from utils.util import pitchyaw_to_gaze_vector


FONT = cv2.FONT_HERSHEY_SIMPLEX
TEXT_SCALE = 0.5
TEXT_THICKNESS = 2


class TargetOrientation(Enum):
    UP = 82
    DOWN = 84
    LEFT = 81
    RIGHT = 83


# ---------------------------------------------------------------------------
# Convert Pixel & 2D face landmarks into 3D points in camera space --> START
# ---------------------------------------------------------------------------

def get_camera_matrix(intrinsics_path: str) -> Tuple[np.ndarray, np.ndarray]:
    """
    Load camera intrinsic matrix and distortion coefficients from a YAML calibration file.

    Args:
        intrinsics_path: Path to the YAML file containing camera calibration parameters

    Returns:
        Tuple containing:
        - camera_matrix: 3x3 intrinsic camera matrix
        - dist_coefficients: Distortion coefficients array
    """
    with open(intrinsics_path, 'r') as file:
        calibration_matrix = yaml.safe_load(file)
    camera_matrix = np.asarray(calibration_matrix['camera_matrix']).reshape(3, 3)
    dist_coefficients = np.asarray(calibration_matrix['dist_coeff'])
    return camera_matrix, dist_coefficients


def pixel_to_camera_point(
    point_px: Tuple[int, int],
    monitor_pixels: Tuple[int, int],
    monitor_mm: Tuple[float, float],
    rvec: np.ndarray,
    tvec_cam2scr_mm: np.ndarray
) -> np.ndarray:
    """
    Convert a 2D pixel coordinate on the screen to a 3D point in camera space.

    Args:
        point_px: (x, y) pixel coordinates on the screen
        monitor_pixels: (width, height) of the monitor in pixels
        monitor_mm: (width, height) of the monitor in millimeters
        rvec: Rotation vector (kept for API consistency, currently unused)
        tvec_cam2scr_mm: Translation vector from camera to screen (kept for API consistency, currently unused)

    Returns:
        3D point in camera coordinate system (shape: 3x1)
    """
    # Extract coordinates and dimensions
    px, py = point_px
    W_px, H_px = monitor_pixels  # Monitor resolution in pixels
    W_mm, H_mm = monitor_mm      # Monitor physical dimensions in mm

    # Step 1: Convert pixel coordinates to millimeters in the screen coordinate system
    # This maps (0,0) to top-left and (W_px,H_px) to bottom-right of physical screen
    x_mm = px * (W_mm / W_px)  # Scale X coordinate
    y_mm = py * (H_mm / H_px)  # Scale Y coordinate

    # Create 3D point in screen coordinate system (Z=0 is the screen plane)
    screen_pt = np.array([[x_mm], [y_mm], [0.0]], dtype=np.float32)

    # Step 2: Define the transformation from screen to camera coordinate system
    # This is a fixed transformation based on the physical setup
    # The rotation matrix flips X and Z axes to align with camera coordinates
    R_scr2cam = np.array([
        [-1.0,  0.0,  0.0],  # X-axis is flipped
        [ 0.0,  1.0,  0.0],  # Y-axis stays the same
        [ 0.0,  0.0, -1.0]   # Z-axis is flipped
    ], dtype=np.float32)

    # Translation vector from screen origin to camera (in mm)
    # These values represent the physical offset between camera and screen
    tvec_scr2cam_mm = np.array([[290.0], [25.0], [15.0]], dtype=np.float32)

    # Step 3: Transform the point from screen to camera coordinate system
    # Apply rotation and translation: p_camera = R * p_screen + t
    pt_cam = R_scr2cam @ screen_pt + tvec_scr2cam_mm

    return pt_cam  # 3D point in camera coordinates (shape: 3x1)


def get_face_landmarks_in_ccs_from_depth(camera_matrix: np.ndarray, depth_frame: np.ndarray, face_landmarks_2d: np.ndarray) -> np.ndarray:
    """
    Convert 2D facial landmarks to 3D camera coordinate system using depth information.

    This function takes 2D pixel coordinates of facial landmarks and uses the corresponding
    depth values to compute their 3D positions in the camera coordinate system.

    Args:
        camera_matrix: 3x3 intrinsic camera matrix containing focal lengths and principal point
        depth_frame: Depth image from depth camera (height x width array) with depth values in meters
        face_landmarks_2d: Array of 2D facial landmarks with shape (N, 2) containing [u, v] pixel coordinates

    Returns:
        3D facial landmarks in camera coordinate system with shape (3, N) containing [X, Y, Z] coordinates
    """
    fx = camera_matrix[0, 0]  # Focal length in x
    fy = camera_matrix[1, 1]  # Focal length in y
    cx = camera_matrix[0, 2]  # Principal point x
    cy = camera_matrix[1, 2]  # Principal point y

    landmarks_3d = []

    for (u, v) in face_landmarks_2d:
        # Ensure integer pixel coordinates and valid depth retrieval
        u, v = int(round(u)), int(round(v))

        if (u < 0) or (u >= depth_frame.shape[1]) or (v < 0) or (v >= depth_frame.shape[0]):
            landmarks_3d.append([0, 0, 0])  # Out-of-bounds handling
            continue

        # Get the depth at the landmark pixel
        d = depth_frame[v, u]  # Depth in meters

        if d == 0:  # If depth is invalid, skip
            landmarks_3d.append([0, 0, 0])
            continue

        # Convert from pixel coordinates (u, v) plus depth d to 3D coordinates
        X = (u - cx) * d / fx
        Y = (v - cy) * d / fy
        Z = d
        landmarks_3d.append([X, Y, Z])

    return np.array(landmarks_3d).T  # Shape: (3, N)

# ===========================================================================
# Convert Pixel & 2D face landmarks into 3D points in camera space --> END
# ===========================================================================

# ---------------------------------------------------------------------------
# Compute Head Rotation Vector (rvec) --> START
# ---------------------------------------------------------------------------

def validate_coordinate_system(x_axis, y_axis, z_axis, threshold=1e-6):
    """
    Validate that the three axes form a proper orthogonal coordinate system.

    A valid coordinate system requires:
    1. All axes are unit vectors (length = 1)
    2. All axes are orthogonal to each other (dot product = 0)
    3. The axes follow the right-hand rule (z = x × y)

    Args:
        x_axis: X-axis unit vector
        y_axis: Y-axis unit vector
        z_axis: Z-axis unit vector
        threshold: Maximum allowed deviation from orthogonality

    Returns:
        is_valid: Boolean indicating if the coordinate system is valid
        issues: List of detected issues (empty if valid)
    """
    issues = []

    # Check if all vectors are unit vectors (length ≈ 1)
    for name, axis in [("X", x_axis), ("Y", y_axis), ("Z", z_axis)]:
        length = np.linalg.norm(axis)
        if abs(length - 1.0) > threshold:
            issues.append(f"{name}-axis is not a unit vector (length = {length:.6f})")

    # Check orthogonality between all pairs of axes
    xy_dot = np.abs(np.dot(x_axis, y_axis))
    yz_dot = np.abs(np.dot(y_axis, z_axis))
    zx_dot = np.abs(np.dot(z_axis, x_axis))

    if xy_dot > threshold:
        issues.append(f"X and Y axes are not orthogonal (dot product = {xy_dot:.6f})")
    if yz_dot > threshold:
        issues.append(f"Y and Z axes are not orthogonal (dot product = {yz_dot:.6f})")
    if zx_dot > threshold:
        issues.append(f"Z and X axes are not orthogonal (dot product = {zx_dot:.6f})")

    # Check right-hand rule: z should equal x × y
    expected_z = np.cross(x_axis, y_axis)
    expected_z /= np.linalg.norm(expected_z)  # Normalize
    z_diff = np.linalg.norm(z_axis - expected_z)

    if z_diff > threshold:
        issues.append(f"Z-axis does not follow right-hand rule (difference = {z_diff:.6f})")

    return len(issues) == 0, issues


def compute_rvec_from_landmarks(landmarks_3d: np.ndarray, validate: bool = True) -> Tuple[np.ndarray, np.ndarray]:
    """
    Compute head rotation vector from 3D facial landmarks in camera coordinate system.

    This function estimates head pose by:
    1. Identifying key facial landmarks (eyes, mouth corners)
    2. Constructing an orthogonal coordinate system based on facial geometry
    3. Creating a rotation matrix representing head orientation relative to camera
    4. Converting the rotation matrix to a compact rotation vector (Rodrigues format)

    The coordinate system is defined as:
    - X-axis: From right eye to left eye (horizontal facial axis)
    - Y-axis: From eye center to mouth center (vertical facial axis)
    - Z-axis: Perpendicular to face plane (forward direction)

    Args:
        landmarks_3d: 3D facial landmarks with shape (3, N) in camera coordinate system
        validate: Whether to validate the orthogonality of the computed coordinate system

    Returns:
        Tuple containing:
        - rvec: Head rotation vector in Rodrigues format (3x1)
        - R: Head rotation matrix (3x3)
    """
    # Define landmark indices for key facial features
    right_eye_indices = [33, 133]      # Subject's right eye (from camera view: left)
    left_eye_indices = [362, 263]      # Subject's left eye (from camera view: right)
    mouth_indices = [61, 291]          # Mouth corners

    # Compute key points by averaging landmark positions
    right_eye = np.mean(landmarks_3d[:, right_eye_indices], axis=1)
    left_eye = np.mean(landmarks_3d[:, left_eye_indices], axis=1)
    mouth_center = np.mean(landmarks_3d[:, mouth_indices], axis=1)
    eye_center = 0.5 * (left_eye + right_eye)

    # Step 1: Construct initial coordinate system
    # X-axis: from right eye to left eye (horizontal axis)
    x_axis = left_eye - right_eye
    x_axis /= np.linalg.norm(x_axis)

    # Y-axis: from eye center to mouth center (vertical axis)
    y_axis = mouth_center - eye_center
    y_axis /= np.linalg.norm(y_axis)

    # Step 2: Ensure orthogonality using cross products
    # Z-axis: perpendicular to both X and Y (forward direction)
    z_axis = np.cross(x_axis, y_axis)
    z_axis /= np.linalg.norm(z_axis)

    # Re-orthogonalize Y-axis to ensure strict orthogonality
    y_axis = np.cross(z_axis, x_axis)
    y_axis /= np.linalg.norm(y_axis)

    # Step 3: Validate the coordinate system if requested
    if validate:
        is_valid, issues = validate_coordinate_system(x_axis, y_axis, z_axis)

        if not is_valid:
            print("WARNING: Invalid coordinate system detected:")
            for issue in issues:
                print(f"  - {issue}")
            print("Proceeding with corrected axes.")

    # Step 4: Create rotation matrix and convert to rotation vector
    # Rotation matrix: columns are the axes of face in camera space
    R = np.stack([x_axis, y_axis, z_axis], axis=1)  # Shape (3, 3)

    # Convert rotation matrix to rotation vector (Rodrigues)
    rvec, _ = cv2.Rodrigues(R)

    return rvec, R

# ===========================================================================
# Compute Head Rotation Vector (rvec) --> END
# ===========================================================================

# ---------------------------------------------------------------------------
# Normalize Image & Gaze --> START
# ---------------------------------------------------------------------------

def get_normalization_matrices(
    camera_matrix: np.ndarray,
    distance_norm: int,
    center_point: np.ndarray,
    focal_norm: int,
    head_rotation_matrix: np.ndarray,
    image_output_size: Tuple[int, int]
) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Calculate matrices needed for gaze data normalization.

    This function computes the rotation, scaling, and transformation matrices
    required to normalize face/eye images to a canonical view.

    Args:
        camera_matrix: Intrinsic camera matrix (3x3) containing focal lengths and principal point
        distance_norm: Target normalized distance between camera and face/eye
        center_point: 3D position of the center point (face or eye center) in camera coordinates
        focal_norm: Target normalized focal length for the normalized camera
        head_rotation_matrix: 3x3 rotation matrix representing head pose
        image_output_size: Dimensions (width, height) of the output normalized image

    Returns:
        Tuple containing:
        - rotation_matrix: 3x3 matrix to rotate points to normalized view
        - scaling_matrix: 3x3 matrix to scale distances
        - transformation_matrix: 3x3 matrix for the complete image transformation
    """
    # Step 1: Calculate distance and scaling factor
    # The actual distance between center point and camera
    actual_distance = np.linalg.norm(center_point)
    # Scale factor to achieve the target normalized distance
    z_scale = distance_norm / actual_distance

    # Step 2: Create normalized camera matrix with desired parameters
    normalized_camera_matrix = np.array([
        [focal_norm,    0,          image_output_size[0] / 2],  # fx and cx
        [0,             focal_norm, image_output_size[1] / 2],  # fy and cy
        [0,             0,          1.0                     ],  # homogeneous coordinate
    ])

    # Step 3: Create scaling matrix (only scales in Z direction)
    scaling_matrix = np.array([
        [1.0, 0.0, 0.0    ],  # No scaling in X
        [0.0, 1.0, 0.0    ],  # No scaling in Y
        [0.0, 0.0, z_scale],  # Scale in Z to achieve normalized distance
    ])

    # Step 4: Compute the new coordinate system axes
    # Forward axis (Z) - direction from camera to center point
    forward_axis = (center_point / actual_distance)

    # Down axis (Y) - perpendicular to forward and head's right direction
    down_axis = np.cross(forward_axis, head_rotation_matrix[:, 0])
    down_axis /= np.linalg.norm(down_axis)  # Normalize to unit vector

    # Right axis (X) - perpendicular to down and forward
    right_axis = np.cross(down_axis, forward_axis)
    right_axis /= np.linalg.norm(right_axis)  # Normalize to unit vector

    # Step 5: Create rotation matrix from the three axes
    rotation_matrix = np.asarray([right_axis, down_axis, forward_axis])

    # Step 6: Compute the complete transformation matrix
    # This combines: original camera → 3D world → rotated 3D world → scaled 3D world → normalized camera
    transformation_matrix = np.dot(
        np.dot(normalized_camera_matrix, scaling_matrix),
        np.dot(rotation_matrix, np.linalg.inv(camera_matrix))
    )

    return rotation_matrix, scaling_matrix, transformation_matrix


def equalize_hist_rgb(rgb_img: np.ndarray) -> np.ndarray:
    """
    Enhance image contrast by equalizing the histogram of an RGB image.

    This function improves image quality by:
    1. Converting the image to YCrCb color space
    2. Equalizing only the luminance (Y) channel
    3. Converting back to RGB

    Args:
        rgb_img: Input RGB image (not BGR)

    Returns:
        Contrast-enhanced RGB image with equalized histogram
    """
    # Step 1: Convert from RGB to YCrCb color space
    # YCrCb separates luminance (Y) from chrominance (Cr, Cb)
    ycrcb_img = cv2.cvtColor(rgb_img, cv2.COLOR_RGB2YCrCb)

    # Step 2: Equalize only the Y (luminance) channel
    # This enhances contrast while preserving color information
    ycrcb_img[:, :, 0] = cv2.equalizeHist(ycrcb_img[:, :, 0])

    # Step 3: Convert back to RGB color space
    equalized_img = cv2.cvtColor(ycrcb_img, cv2.COLOR_YCrCb2RGB)

    return equalized_img


def normalize_single_image(
    image: np.ndarray,
    head_rotation: np.ndarray,
    gaze_target: np.ndarray,
    center_point: np.ndarray,
    camera_matrix: np.ndarray,
    is_eye: bool = True
) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Normalize an image to a canonical view based on 3D geometry.

    This function performs data normalization for gaze estimation by:
    1. Transforming the image to a canonical view based on head pose
    2. Normalizing the gaze vector to the new coordinate system

    Args:
        image: Original RGB image
        head_rotation: Head rotation vector (3x1) in Rodrigues format
        gaze_target: 3D target point of the gaze in camera coordinates (can be None)
        center_point: 3D point to center the normalized view on (eye or face center)
        camera_matrix: Intrinsic camera matrix (3x3)
        is_eye: If True, use parameters for eye normalization; if False, use face parameters

    Returns:
        Tuple containing:
        - normalized_image: Warped and normalized image
        - normalized_gaze: Gaze vector in the normalized space
        - rotation_matrix: Rotation matrix used for normalization
    """
    # Step 1: Set normalization parameters based on whether we're normalizing an eye or face
    focal_norm = 960  # Standard focal length for the normalized camera

    # Different parameters for eye vs face normalization
    if is_eye:
        distance_norm = 500      # Normalized distance for eye (in arbitrary units)
        image_output_size = (96, 64)  # Width, height for eye image
    else:
        distance_norm = 1600     # Normalized distance for face (in arbitrary units)
        image_output_size = (96, 96)  # Width, height for face image

    # Step 2: Convert head rotation from Rodrigues format to rotation matrix
    head_rotation_matrix, _ = cv2.Rodrigues(head_rotation)

    # Step 3: Compute normalization matrices
    rotation_matrix, _, transformation_matrix = get_normalization_matrices(
        camera_matrix,
        distance_norm,
        center_point,
        focal_norm,
        head_rotation_matrix,
        image_output_size
    )

    # Step 4: Warp the image to normalized view
    normalized_image = cv2.warpPerspective(
        image, transformation_matrix, image_output_size
    )

    # Step 5: Enhance image contrast through histogram equalization
    normalized_image = equalize_hist_rgb(normalized_image)

    # Step 6: Normalize the gaze vector if provided
    if gaze_target is not None:
        # Calculate gaze vector from center point to target
        gaze_vector = gaze_target - center_point
        gaze_vector = gaze_vector / np.linalg.norm(gaze_vector)  # Normalize to unit vector

        # Transform gaze vector to normalized space
        # Note: Only rotation is applied, not scaling
        normalized_gaze = np.dot(rotation_matrix, gaze_vector)
        normalized_gaze = normalized_gaze / np.linalg.norm(normalized_gaze)  # Ensure unit vector
    else:
        # If no gaze target provided, return zero vector
        gaze_vector = np.zeros(3)
        normalized_gaze = np.zeros(3)

    return normalized_image, gaze_vector, normalized_gaze, rotation_matrix

# ===========================================================================
# Normalize Image & Gaze --> END
# ===========================================================================

# ---------------------------------------------------------------------------
# Verify Gaze Vector --> START
# ---------------------------------------------------------------------------

def normalize(v: np.ndarray) -> np.ndarray:
    """
    Normalize a vector to unit length.

    Args:
        v: Input vector

    Returns:
        Unit vector in the same direction
    """
    return v / np.linalg.norm(v)


def verify_gaze_vector_loss(gaze_target: np.ndarray, face_center: np.ndarray, rotation_matrix: np.ndarray, atol: float = 1e-5, verbose: bool = False) -> None:
    """
    Verify the consistency of gaze vector transformations through forward and inverse operations.

    This function performs a round-trip test to ensure that:
    1. Converting gaze vector to pitch/yaw angles
    2. Converting pitch/yaw back to gaze vector
    3. Transforming between coordinate systems

    Results in the original gaze vector (within numerical tolerance).

    Args:
        gaze_target: 3D target point that the person is looking at
        face_center: 3D position of the face center
        rotation_matrix: Rotation matrix used for coordinate transformation
        atol: Absolute tolerance for numerical comparisons
        verbose: Whether to print detailed comparison results

    Raises:
        AssertionError: If the round-trip transformation exceeds tolerance
    """
    # 1. Original gaze vector (normalized)
    gaze_vector = gaze_target - face_center
    gaze_vector_norm = gaze_vector / np.linalg.norm(gaze_vector)

    # Rotate it to get normalized gaze (camera-centric)
    normalized_gaze = np.dot(rotation_matrix, gaze_vector_norm)
    normalized_gaze = normalized_gaze / np.linalg.norm(normalized_gaze)

    # Compute pitch and yaw from normalized gaze
    pitch = np.arcsin(-normalized_gaze[1])
    yaw = np.arctan2(-normalized_gaze[0], -normalized_gaze[2])

    # 2. Inverse computation: recover normalized_gaze from the pitch and yaw angles
    recovered_normalized_gaze = pitchyaw_to_gaze_vector(pitch, yaw)
    recovered_normalized_gaze = recovered_normalized_gaze / np.linalg.norm(recovered_normalized_gaze)

    # 3. Inverse computation: recover original gaze vector
    recovered_gaze_vector = np.dot(rotation_matrix.T, recovered_normalized_gaze)
    recovered_gaze_vector = recovered_gaze_vector / np.linalg.norm(recovered_gaze_vector)

    # 4. Compare with original normalized vector
    cosine_similarity = np.dot(gaze_vector_norm.flatten(), recovered_gaze_vector.flatten())
    angle_diff_deg = np.degrees(np.arccos(np.clip(cosine_similarity, -1.0, 1.0)))

    # 5. Verify
    assert np.allclose(normalized_gaze, recovered_normalized_gaze, atol=atol), f"{normalized_gaze} != {recovered_normalized_gaze}"
    assert np.allclose(cosine_similarity, 1.0, atol=atol), f"{cosine_similarity} != {1.0}"
    assert np.allclose(angle_diff_deg, 0.0, atol=atol), f"{angle_diff_deg} != {0.0}"

    if verbose:
        print("Original gaze vector (normalized):\n", gaze_vector_norm)
        print("Recovered gaze vector:\n", recovered_gaze_vector)
        print("Cosine similarity:", cosine_similarity)
        print("Angular difference (deg):", angle_diff_deg)

# ===========================================================================
# Verify Gaze Vector --> END
# ===========================================================================
