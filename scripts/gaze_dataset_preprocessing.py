"""
Gaze Dataset Preprocessing Module

This module handles the preprocessing of raw gaze data to create normalized
face and eye images along with corresponding gaze labels for training.

The preprocessing pipeline:
1. Loads raw images and depth data
2. Extracts 3D face landmarks using depth information
3. Computes head pose from landmarks
4. Normalizes face and eye regions to canonical views
5. Computes pitch and yaw angles from 3D gaze vectors
6. Saves normalized images and creates a labels CSV file
"""
import os
import sys
import ast
from tqdm import tqdm
from pathlib import Path

import cv2
import numpy as np
import pandas as pd

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.config import get_config
from scripts.gaze_dataset_utils import (
    get_camera_matrix,
    pixel_to_camera_point,
    get_face_landmarks_in_ccs_from_depth,
    compute_rvec_from_landmarks,
    normalize_single_image,
    verify_gaze_vector_loss
)


def preprocess_gaze_dataset(raw_base: str, normalized_base: str, camera_matrix) -> None:
    """
    Process raw gaze data to produce normalized face/eye crops and labels CSV.

    This function:
    1. Loads raw images, depth data, and metadata
    2. Computes 3D gaze targets and face landmarks
    3. Normalizes face and eye regions
    4. Computes gaze angles (pitch and yaw)
    5. Saves normalized images and creates a labels CSV

    Args:
        raw_base: Path to directory containing raw data and data.csv
        normalized_base: Path where normalized data will be saved
        camera_matrix: 3x3 camera intrinsic matrix for depth-to-3D conversion
    """
    # Initialize list to collect entries for the labels CSV
    labels = []
    skipped_count = 0
    processed_count = 0

    # Step 1: Read the raw data CSV file
    df = pd.read_csv(raw_base / 'data.csv')

    # Step 2: Process each sample
    for idx, row in tqdm(df.iterrows(), total=len(df), desc=f"Processing {raw_base.name}'s rows"):
        # Extract session, filename
        path = Path(row['file_name'])
        session, filename, basename = path.parent, path.name, path.stem

        # Step 3: Load image and depth data
        img_path = raw_base / session / filename
        depth_path = raw_base / session / f"{basename}_depth.npy"
        landmarks_path = raw_base / session / f"{basename}_landmarks.npy"

        if not (img_path.exists() and depth_path.exists() and landmarks_path.exists()):
            print(f"Error: One of the files not found: {raw_base / session / basename}, skipping at {idx}.")
            skipped_count += 1
            continue

        # Load depth data
        img = cv2.imread(str(img_path))
        depth = np.load(str(depth_path))
        landmarks_2D_norm = np.load(str(landmarks_path))  # Normalized landmarks (x,y,z)

        h, w, _ = img.shape                             # Image dimensions
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)  # Convert to RGB for processing

        # Step 4: Parse metadata from CSV
        # Parse screen and camera parameters
        point_on_screen = ast.literal_eval(row['point_on_screen'])          # Gaze target in pixels
        monitor_mm = ast.literal_eval(row['monitor_mm'])                    # Monitor dimensions in mm
        monitor_pixels = ast.literal_eval(row['monitor_pixels'])            # Monitor resolution
        rvec = np.asarray(ast.literal_eval(row['rvec']), dtype=float)       # Camera rotation
        tvec_mm = np.asarray(ast.literal_eval(row['tvec']), dtype=float)    # Camera translation

        # Step 5: Compute 3D gaze target in camera coordinates
        gaze_target = pixel_to_camera_point(
            point_on_screen, monitor_pixels, monitor_mm, rvec, tvec_mm
        ).flatten()

        # Step 6:Process face landmarks
        # Convert normalized landmarks to pixel coordinates
        landmarks_2D = np.array([[x * w, y * h] for x, y, _ in landmarks_2D_norm])

        # Convert 2D landmarks to 3D using depth information
        landmarks_3D = get_face_landmarks_in_ccs_from_depth(
            camera_matrix, depth, landmarks_2D
        )

        # Step 7: Compute key points for normalization
        # Extract eye centers and face center from landmarks
        left_eye_center = landmarks_3D[:, 468].flatten()  # Left eye landmark
        right_eye_center = landmarks_3D[:, 473].flatten()  # Right eye landmark
        face_center = landmarks_3D.mean(axis=1).flatten()  # Average of all landmarks

        # Skip if any of the key points are zero vectors
        zero_vector = np.array([0, 0, 0])
        if (
            np.array_equal(gaze_target, zero_vector)
            or np.array_equal(face_center, zero_vector)
            or np.array_equal(left_eye_center, zero_vector)
            or np.array_equal(right_eye_center, zero_vector)
        ):
            print(f"{zero_vector} skipping at {idx}: {path}")
            skipped_count += 1
            continue
        
        # Step 8: Compute head pose from 3D landmarks
        rvec_head, _ = compute_rvec_from_landmarks(landmarks_3D)

        # Step 9: Normalize face and eye images
        # Normalize face image with gaze target
        face_img, gaze_vector, normalized_gaze_vector, rotation_matrix = normalize_single_image(
            img_rgb, rvec_head, gaze_target, face_center, camera_matrix, is_eye=False
        )
        # Normalize eye images (no gaze target needed)
        left_img = normalize_single_image(
            img_rgb, rvec_head, None, left_eye_center, camera_matrix, is_eye=True
        )[0]
        right_img = normalize_single_image(
            img_rgb, rvec_head, None, right_eye_center, camera_matrix, is_eye=True
        )[0]

        # Step 10: Compute pitch and yaw angles from normalized gaze vector
        # Pitch: angle between gaze vector and XZ plane
        pitch = np.arcsin(-normalized_gaze_vector[1])

        # Yaw: angle between projection of gaze vector on XZ plane and Z axis
        yaw = np.arctan2(-normalized_gaze_vector[0], -normalized_gaze_vector[2])

        # Step 11: Verify the computed pitch and yaw angles
        verify_gaze_vector_loss(gaze_target, face_center, rotation_matrix)

        # Create session folder if it doesn't exist
        session_folder = normalized_base / session
        session_folder.mkdir(parents=True, exist_ok=True)

        # Step 12: Save normalized images
        face_file = session_folder / f"{basename}-face.jpg"
        left_file = session_folder / f"{basename}-left-eye.jpg"
        right_file = session_folder / f"{basename}-right-eye.jpg"

        # Convert back to BGR for OpenCV's imwrite
        params = [cv2.IMWRITE_JPEG_QUALITY, 100]
        cv2.imwrite(str(face_file), cv2.cvtColor(face_img, cv2.COLOR_RGB2BGR), params)
        cv2.imwrite(str(left_file), cv2.cvtColor(left_img, cv2.COLOR_RGB2BGR), params)
        cv2.imwrite(str(right_file), cv2.cvtColor(right_img, cv2.COLOR_RGB2BGR), params)

        # Step 13: Add entry to labels list
        labels.append({
            'face_file_name': f"{session}/{face_file.name}",
            'left_eye': f"{session}/{left_file.name}",
            'right_eye': f"{session}/{right_file.name}",
            'pitch': pitch,
            'yaw': yaw,
            'gaze_vector_x': gaze_vector[0],
            'gaze_vector_y': gaze_vector[1],
            'gaze_vector_z': gaze_vector[2],
            'normalized_gaze_vector_x': normalized_gaze_vector[0],
            'normalized_gaze_vector_y': normalized_gaze_vector[1],
            'normalized_gaze_vector_z': normalized_gaze_vector[2],
            'gaze_target_x': gaze_target[0],
            'gaze_target_y': gaze_target[1],
            'gaze_target_z': gaze_target[2],
            'face_center_x': face_center[0],
            'face_center_y': face_center[1],
            'face_center_z': face_center[2],
            'left_eye_center_x': left_eye_center[0],
            'left_eye_center_y': left_eye_center[1],
            'left_eye_center_z': left_eye_center[2],
            'right_eye_center_x': right_eye_center[0],
            'right_eye_center_y': right_eye_center[1],
            'right_eye_center_z': right_eye_center[2],
        })

        processed_count += 1

    # Step 14: Write labels to CSV file
    if labels:
        labels_df = pd.DataFrame(labels)
        labels_csv_path = normalized_base / 'labels.csv'
        labels_df.to_csv(labels_csv_path, index=False)
        print(f"Saved {len(labels)} entries to {labels_csv_path}")
    else:
        print("No valid samples were processed. No labels.csv file created.")

    # Print summary
    print(f"\nPreprocessing Summary:")
    print(f"  Total samples: {len(df)}")
    print(f"  Successfully processed: {processed_count}")
    print(f"  Skipped: {skipped_count}")
    print(f"  Success rate: {processed_count/len(df)*100:.1f}%\n\n")


def main():
    # Load configuration from file and update with command-line arguments
    config = get_config()

    # Convert paths to Path objects for easier handling
    dataset_path_raw = Path(config['data_preprocessing']['dataset_path'])
    dataset_path_normalized = Path(config['data_preprocessing']['output_path'])
    intrinsics_path = Path(config['camera']['intrinsics_path'])

    # Print configuration
    print("\n** Gaze Dataset Preprocessing **")
    print("="*60)
    print(f"Input path: {dataset_path_raw}")
    print(f"Output path: {dataset_path_normalized}")
    print(f"Camera matrix YAML path: {intrinsics_path}")
    print("="*60)

    # Load camera calibration parameters
    camera_matrix = get_camera_matrix(intrinsics_path)[0]

    for _set in ['train', 'test']:
        raw_base = dataset_path_raw / _set
        normalized_base = dataset_path_normalized / _set

        # Run the main preprocessing function
        preprocess_gaze_dataset(raw_base, normalized_base, camera_matrix)

    print("Preprocessing complete!")
    print(f"The normalized dataset path: {dataset_path_normalized}")


if __name__ == '__main__':
    main()
