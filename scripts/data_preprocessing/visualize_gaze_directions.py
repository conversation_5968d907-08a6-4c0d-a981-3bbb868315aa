import os
import sys
from tqdm import tqdm
from pathlib import Path

import cv2
import torch
import pandas as pd
import albumentations as A
from albumentations.pytorch import ToTensorV2
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from nets.nn import FinalModel
from utils.config import get_config
from utils.util import pitchyaw_to_gaze_vector


device = 'cuda' if torch.cuda.is_available() else 'cpu'


def draw_gaze_arrow(img, pitch, yaw, color=(0, 0, 255), thickness=2, length=50):
    """
    Draw gaze arrow on an image based on pitch and yaw angles.

    Args:
        img: Input image
        pitch: Pitch angle in radians
        yaw: Yaw angle in radians
        color: Arrow color in BGR format (default: red)
        thickness: Arrow thickness
        length: Arrow length in pixels

    Returns:
        Image with drawn arrow
    """
    h, w = img.shape[:2]
    center = (w // 2, h // 2)

    # Get 3D gaze vector (x, y components for 2D projection)
    x, y, _ = pitchyaw_to_gaze_vector(pitch, yaw)

    # Project to 2D (image plane), ignoring Z
    dx = x * length
    dy = y * length

    # Calculate end point
    end_point = (int(center[0] + dx), int(center[1] + dy))

    # Draw arrow
    cv2.arrowedLine(img, center, end_point, color, thickness, tipLength=0.3)

    return img


def load_gaze_model(model_ckpt_path: str) -> FinalModel:
    """
    Load a gaze tracking model from a checkpoint file.

    Args:
        model_ckpt_path: Path to the model checkpoint file

    Returns:
        Loaded model in evaluation mode, or None if loading fails
    """
    model = None

    if model_ckpt_path:
        try:
            model = FinalModel()
            checkpoint = torch.load(model_ckpt_path, map_location=device, weights_only=True)
            model.load_state_dict(checkpoint['state_dict'])
            model.to(device)
            model.eval()
            print(f"Model loaded from: {model_ckpt_path}\n")
        except Exception as e:
            print(f"Error loading model: {e}")
    return model


def visualize_gaze(subset_dataset_path: Path,
                   subset_output_path: Path,
                   model: FinalModel = None,
                   arrow_length: int = 50,
                   arrow_thickness: int = 2) -> None:
    """
    Visualize gaze direction with ground truth (green) and prediction (red) arrows.

    Args:
        subset_dataset_path: Path to dataset directory containing labels.csv and images
        subset_output_path: Path to save visualization results
        model: Trained gaze tracking model (if None, only ground truth is shown)
        arrow_length: Length of the gaze arrow in pixels
        arrow_thickness: Thickness of the gaze arrow
    """
    # Create output directory
    os.makedirs(subset_output_path, exist_ok=True)

    # Load CSV with gaze data
    df = pd.read_csv(subset_dataset_path / 'labels.csv')

    # Image transform for model input
    transform = A.Compose([
        A.Normalize(),
        ToTensorV2()
    ])

    # Process each sample
    for idx, row in tqdm(df.iterrows(), total=len(df), desc=f"Processing {subset_output_path.name}'s rows"):
        # Get file paths
        face_img_path = os.path.join(subset_dataset_path, row['face_file_name'])

        # Load face image
        face_img = cv2.imread(face_img_path)
        if face_img is None:
            print(f"Failed to load image: {face_img_path}")
            continue

        # Create a copy of the image for visualization
        img = face_img.copy()

        # If model is available, add prediction arrow
        if model:
            try:
                # Load eye images
                left_eye_path = os.path.join(subset_dataset_path, row['left_eye'])
                right_eye_path = os.path.join(subset_dataset_path, row['right_eye'])
                left_img = cv2.imread(left_eye_path)
                right_img = cv2.imread(right_eye_path)

                if left_img is None or right_img is None:
                    print(f"Missing eye image at {idx}")
                    continue

                # Preprocess images
                face_tensor = transform(image=cv2.cvtColor(face_img, cv2.COLOR_BGR2RGB))['image'].unsqueeze(0).to(device)
                left_tensor = transform(image=cv2.cvtColor(left_img, cv2.COLOR_BGR2RGB))['image'].unsqueeze(0).to(device)
                right_tensor = transform(image=cv2.cvtColor(right_img, cv2.COLOR_BGR2RGB))['image'].unsqueeze(0).to(device)

                # Create dummy person index
                person_idx = torch.tensor([0], dtype=torch.long).to(device)

                # Verify input shapes
                if not (face_tensor.shape == (1, 3, 96, 96) and
                        right_tensor.shape == (1, 3, 64, 96) and
                        left_tensor.shape == (1, 3, 64, 96)):
                    print(f"Shape mismatch at {idx}: face={face_tensor.shape}, right={right_tensor.shape}, left={left_tensor.shape}")
                    continue

                # Make prediction
                with torch.no_grad():
                    output = model(person_idx, face_tensor, right_tensor, left_tensor)
                    pred_pitch, pred_yaw = output[0].cpu().numpy()

                # Draw prediction arrow (red)
                img = draw_gaze_arrow(img,
                                      pitch=pred_pitch,
                                      yaw=pred_yaw,
                                      color=(0, 0, 255),
                                      thickness=arrow_thickness,
                                      length=arrow_length)

            except Exception as e:
                print(f"Error processing sample {idx}: {e}")

        # Draw ground truth arrow (green)
        img = draw_gaze_arrow(img,
                              pitch=float(row['pitch']),
                              yaw=float(row['yaw']),
                              color=(0, 255, 0),
                              thickness=arrow_thickness,
                              length=arrow_length)

        # Save visualization
        output_file = os.path.join(subset_output_path, f"gaze_{idx}.png")
        cv2.imwrite(output_file, img, params=[cv2.IMWRITE_JPEG_QUALITY, 100])


def main():
    # Load configuration from file and update with command-line arguments
    config = get_config()

    # Convert paths to Path objects for easier handling
    dataset_path = Path(config['gaze_directions_visualization']['dataset_path'])
    output_path = Path(config['gaze_directions_visualization']['output_path'])
    model_ckpt_path = Path(config['gaze_directions_visualization']['model_ckpt_path'])
    arrow_length = config['gaze_directions_visualization']['arrow_length']
    arrow_thickness = config['gaze_directions_visualization']['arrow_thickness']

    print(f"\nLoading data from: {dataset_path}")
    print(f"Saving results to: {output_path}")

    # Load model
    model = load_gaze_model(model_ckpt_path)

    for _set in ['train', 'test']:
        subset_dataset_path = dataset_path / _set
        subset_output_path = output_path / _set

        # Visualize gaze with both ground truth and prediction
        visualize_gaze(
            subset_dataset_path=subset_dataset_path,
            subset_output_path=subset_output_path,
            model=model,
            arrow_length=arrow_length,
            arrow_thickness=arrow_thickness,
        )

    print("\nDrawing gaze arrows complete!")
    print(f"All results saved to: {output_path}")


if __name__ == "__main__":
    main()
