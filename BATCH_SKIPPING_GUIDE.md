# Batch Skipping Implementation Guide

This guide explains the comprehensive batch skipping mechanism implemented to handle NaN predictions during training.

## 🎯 **Problem Solved**

When your model produces NaN predictions during training, the traditional approach would crash the training or corrupt the gradients. Our solution **automatically skips problematic batches** while continuing training with healthy data.

## 🔧 **Implementation Overview**

### Key Components

1. **Comprehensive Input Validation**: Checks all inputs before forward pass
2. **Output Validation**: Detects NaN/Inf in model predictions
3. **Loss Validation**: Ensures loss calculations are valid
4. **Automatic Skipping**: Returns dummy loss for problematic batches
5. **Detailed Logging**: Tracks skip rates and reasons
6. **Phase-Aware**: Handles train/val/test phases separately

### How It Works

```python
def __step(self, batch: dict, phase: str = 'train'):
    # 1. Count total batches
    self._total_batches[phase] += 1
    
    # 2. Validate inputs (face, eyes, labels)
    if torch.isnan(inputs).any():
        self._log_skipped_batch("reason", phase)
        return None  # Skip this batch
    
    # 3. Forward pass
    outputs = self(inputs)
    
    # 4. Validate outputs
    if torch.isnan(outputs).any():
        self._log_skipped_batch("NaN in outputs", phase)
        return None  # Skip this batch
    
    # 5. Calculate and validate losses
    loss = calculate_loss(outputs, labels)
    if torch.isnan(loss).any():
        self._log_skipped_batch("NaN in loss", phase)
        return None  # Skip this batch
    
    # 6. Return valid results
    return loss, labels, outputs
```

## 📊 **Monitoring and Logging**

### Console Output
When a batch is skipped, you'll see:
```
BATCH SKIPPED [TRAIN]: NaN/Inf in model outputs
  Epoch: 5, Skip rate: 2.34% (12/512)
```

### TensorBoard Metrics
- `train/skipped_batches`: Number of skipped batches
- `train/skip_rate`: Percentage of batches skipped
- `val/skipped_batches`: Validation skipped batches
- `val/skip_rate`: Validation skip rate

### Tracking Statistics
```python
# Access skip statistics
model._skipped_batches['train']  # Number of skipped training batches
model._total_batches['train']    # Total training batches processed
skip_rate = model._skipped_batches['train'] / model._total_batches['train']
```

## 🚨 **Validation Points**

### Input Validation
- **Face images**: Check for NaN/Inf in face image tensors
- **Eye images**: Validate both left and right eye images
- **Labels**: Ensure gaze labels (pitch/yaw) are valid numbers

### Model Output Validation
- **Prediction range**: Check if outputs are within reasonable bounds
- **NaN detection**: Identify any NaN values in predictions
- **Inf detection**: Catch infinite values

### Loss Validation
- **Angular loss**: Validate trigonometric calculations
- **L1 loss**: Check regression loss computation
- **Combined loss**: Ensure final weighted loss is valid

## 🎛️ **Configuration Options**

### Skip Behavior
The implementation automatically:
- **Skips problematic batches** without crashing training
- **Returns dummy loss** (0.0) to maintain training flow
- **Continues with next batch** seamlessly
- **Logs detailed information** about skip reasons

### Monitoring Frequency
```python
# Skip rate warnings (configurable)
if skip_rate > 10.0:  # More than 10% skipped
    print(f"WARNING: High skip rate detected: {skip_rate:.2f}%")
```

## 📈 **Benefits**

### Training Stability
- ✅ **No training crashes** due to NaN losses
- ✅ **Continuous training** even with problematic data
- ✅ **Gradient flow preservation** for healthy batches
- ✅ **Automatic recovery** from temporary issues

### Debugging Capabilities
- ✅ **Detailed logging** of skip reasons
- ✅ **Skip rate monitoring** to identify data issues
- ✅ **Phase-specific tracking** (train/val/test)
- ✅ **TensorBoard integration** for visualization

### Data Quality Insights
- ✅ **Identify problematic samples** in your dataset
- ✅ **Monitor model stability** over training
- ✅ **Track improvement** as training progresses
- ✅ **Early warning system** for data corruption

## 🔍 **Interpreting Skip Rates**

### Healthy Training
- **Skip rate < 1%**: Excellent data quality, stable training
- **Skip rate 1-5%**: Good training, minor issues
- **Decreasing skip rate**: Model is stabilizing

### Warning Signs
- **Skip rate > 10%**: Significant data or model issues
- **Increasing skip rate**: Model becoming unstable
- **Skip rate > 50%**: Critical problems, investigate immediately

### Common Skip Reasons
1. **"NaN in model outputs"**: Model instability, check learning rate
2. **"NaN in face images"**: Data corruption, check preprocessing
3. **"Invalid angular loss"**: Extreme predictions, check model bounds
4. **"NaN in labels"**: Label corruption, validate dataset

## 🛠️ **Troubleshooting**

### High Skip Rates
If you see high skip rates:

1. **Check data quality**:
   ```bash
   python scripts/debug_nan_issues.py
   ```

2. **Reduce learning rate**:
   ```yaml
   training:
     learning_rate: 0.000005  # Even lower
   ```

3. **Increase regularization**:
   ```yaml
   training:
     weight_decay: 0.001
   ```

4. **Adjust loss weights**:
   ```yaml
   training:
     loss_weights:
       angular: 0.1  # Reduce angular loss influence
       l1: 1.0
   ```

### Zero Skip Rates
If you never see skipped batches but still get NaN losses:
- Check if the validation is working correctly
- Verify the angular_loss function improvements are applied
- Ensure gradient clipping is enabled

## 🚀 **Best Practices**

### Monitoring
- **Watch skip rates** in TensorBoard
- **Set up alerts** for high skip rates (>10%)
- **Log skip reasons** for debugging
- **Track trends** over epochs

### Data Quality
- **Validate datasets** before training
- **Check preprocessing** pipelines
- **Monitor input ranges** regularly
- **Use data quality checks**

### Model Stability
- **Start with lower learning rates**
- **Use gradient clipping**
- **Enable full precision training**
- **Monitor gradient norms**

## 📋 **Summary**

The batch skipping implementation provides:

1. **Robust Training**: Automatically handles NaN predictions without crashes
2. **Detailed Monitoring**: Comprehensive logging and metrics
3. **Easy Debugging**: Clear skip reasons and statistics
4. **Seamless Integration**: Works with existing PyTorch Lightning workflow
5. **Performance Preservation**: Minimal overhead, maximum stability

Your training will now continue smoothly even when encountering problematic batches, while providing you with detailed information to identify and fix underlying issues.

## 🔗 **Related Files**

- `main.py`: Core implementation
- `utils/util.py`: Improved angular_loss function
- `scripts/debug_nan_issues.py`: Debugging tools
- `NaN_LOSS_TROUBLESHOOTING_GUIDE.md`: Comprehensive troubleshooting

The batch skipping mechanism ensures your training remains stable and productive while giving you the insights needed to improve your model and data quality.
