import torch
import numpy as np
import pandas as pd
import torch.nn.functional as F
from pytorch_lightning import Callback

from utils.util import calculate_gaze_angle_error


class MetricsLogger(Callback):
    """
    PyTorch Lightning callback for logging detailed training metrics.

    This callback periodically evaluates the model on training, validation, and optionally
    test sets, then logs the results to console and saves them to a CSV file for analysis.

    Args:
        test_loader: Optional test data loader for evaluation during training
        freq_epochs: Frequency (in epochs) for logging metrics
    """

    def __init__(self, test_loader=None, freq_epochs: int = 5):
        super().__init__()
        self.records = []
        self.freq_epochs = freq_epochs
        self.test_loader = test_loader

    def on_train_epoch_end(self, trainer, pl_module):
        """
        Called at the end of each training epoch to log metrics.

        This method:
        1. Checks if it's time to log (based on freq_epochs)
        2. Extracts training and validation metrics from the trainer
        3. Optionally evaluates on test set if test_loader is provided
        4. Prints metrics to console in a formatted way
        5. Saves all metrics to a CSV file for later analysis

        Args:
            trainer: PyTorch Lightning trainer instance
            pl_module: The lightning module being trained
        """
        epoch = trainer.current_epoch
        if epoch % self.freq_epochs != 0:
            return

        # Extract training and validation metrics from trainer
        m = trainer.callback_metrics
        tr_l, tr_e = m["train/loss"].item(), m["train/angular_error"].item()
        vl_l, vl_e = m["val/loss"].item(), m["val/angular_error"].item()

        # Evaluate on test set if test loader is provided
        if self.test_loader:
            pl_module.eval()
            test_losses, test_errors = [], []

            with torch.no_grad():
                for batch in self.test_loader:
                    # Move all tensor data to the correct device
                    for k, v in batch.items():
                        if torch.is_tensor(v):
                            batch[k] = v.to(pl_module.device)

                    # Forward pass through the model
                    preds = pl_module(
                        batch['person_idx'],
                        batch['full_face_image'],
                        batch['right_eye_image'],
                        batch['left_eye_image']
                    )

                    # Prepare ground truth labels
                    labels = torch.stack([batch['gaze_pitch'], batch['gaze_yaw']], dim=1)

                    # Calculate metrics
                    test_losses.append(F.mse_loss(preds, labels).item())
                    test_errors.append(calculate_gaze_angle_error(labels, preds)[0].item())

            # Compute average test metrics
            ts_l = float(np.mean(test_losses))
            ts_e = float(np.mean(test_errors))
            pl_module.train()  # Return to training mode
        else:
            ts_l = ts_e = None

        # Print formatted metrics to console
        print(f"\n\t‣ Train: loss={tr_l:.4f}, angular_error={tr_e:.2f}°")
        print(f"\t‣ Val:   loss={vl_l:.4f}, angular_error={vl_e:.2f}°")
        if ts_l is not None:
            print(f"\t‣ Test:  loss={ts_l:.4f}, angular_error={ts_e:.2f}°")

        # Record metrics and save to CSV file
        self.records.append({
            "epoch": epoch,
            "train_loss": tr_l, "train_err": tr_e,
            "val_loss": vl_l, "val_err": vl_e,
            "test_loss": ts_l, "test_err": ts_e
        })
        log_dir = trainer.logger.log_dir
        pd.DataFrame(self.records).to_csv(f"{log_dir}/training_loss_details.csv", index=False)
