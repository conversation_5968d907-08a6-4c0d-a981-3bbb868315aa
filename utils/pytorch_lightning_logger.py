import torch
import numpy as np
import pandas as pd
import torch.nn.functional as F
from pytorch_lightning import Callback

from utils.util import calc_angle_error


class MetricsLogger(Callback):
    def __init__(self, test_loader=None, freq_epochs=5):
        super().__init__()
        self.records = []
        self.freq_epochs = freq_epochs
        self.test_loader = test_loader

    def on_train_epoch_end(self, trainer, pl_module):
        epoch = trainer.current_epoch
        if epoch % self.freq_epochs != 0:
            return

        m = trainer.callback_metrics
        tr_l, tr_e = m["train/loss"].item(), m["train/angular_error"].item()
        vl_l, vl_e = m["val/loss"].item(), m["val/angular_error"].item()

        # Manual test loop
        if self.test_loader:
            pl_module.eval()
            test_losses, test_errors = [], []
            
            with torch.no_grad():
                for batch in self.test_loader:
                    # Move tensors to device
                    for k, v in batch.items():
                        if torch.is_tensor(v):
                            batch[k] = v.to(pl_module.device)
                    preds = pl_module(
                        batch['person_idx'],
                        batch['full_face_image'],
                        batch['right_eye_image'],
                        batch['left_eye_image']
                    )
                    labels = torch.stack([batch['gaze_pitch'], batch['gaze_yaw']], dim=1)
                    test_losses.append(F.l1_loss(preds, labels).item())
                    test_errors.append(calc_angle_error(labels, preds).item())
            ts_l = float(np.mean(test_losses))
            ts_e = float(np.mean(test_errors))
            pl_module.train()
        else:
            ts_l = ts_e = None

        # Console print
        print(f"\n\t‣ train loss={tr_l:.4f}, err={tr_e:.2f}")
        print(f"\t‣ val   loss={vl_l:.4f}, err={vl_e:.2f}")
        if ts_l is not None:
            print(f"\t‣ test  loss={ts_l:.4f}, err={ts_e:.2f}")

        # Record & dump CSV
        self.records.append({
            "epoch": epoch,
            "train_loss": tr_l, "train_err": tr_e,
            "val_loss": vl_l, "val_err": vl_e,
            "test_loss": ts_l, "test_err": ts_e
        })
        log_dir = trainer.logger.log_dir
        pd.DataFrame(self.records).to_csv(f"{log_dir}/training_loss_details.csv", index=False)
