import os
import io
import csv
import sys
import cv2
import yaml
import time
import copy
import math
import onnx
import numpy
import torch
import random
import argparse
import torchmetrics
import pandas as pd
import seaborn as sn
import matplotlib.pyplot as plt

from pathlib import Path
from timeit import timeit
from loguru import logger
from functools import wraps
from platform import system
from onnxsim import simplify
from datetime import datetime


def init_deterministic_seed(seed=0, deterministic_mode=True):
    """ Setup random seed """
    random.seed(seed)
    numpy.random.seed(seed)
    torch.manual_seed(seed)
    torch.backends.cudnn.benchmark = not deterministic_mode
    torch.backends.cudnn.deterministic = deterministic_mode


def timer(func):
    """ Time decorator """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()

        time_taken = (end_time - start_time)

        if time_taken > 60:
            time_taken = f"{time_taken / 60.0:.2f} minutes"
        elif time_taken > 3600:
            time_taken = f"{time_taken / 3600.0:.2f} hours"
        elif time_taken > 86400:
            time_taken = f"{time_taken / 86400.0:.2f} days"
        else:
            time_taken = f"{time_taken:.2f} seconds"
        logger.warning(f"Function '{func.__name__}' took {time_taken} to complete.")
        return result
    return wrapper


def setup_multi_processes():
    """ Setup multi-processing environment variables """

    # Set multiprocess start method as `fork` to speed up the training
    if system() != 'Windows':
        torch.multiprocessing.set_start_method('fork', force=True)

    # Disable opencv multithreading to avoid system being overloaded (incompatible with PyTorch DataLoader)
    cv2.setNumThreads(0)

    # Setup OMP threads
    if 'OMP_NUM_THREADS' not in os.environ:
        os.environ['OMP_NUM_THREADS'] = '16'

    # Setup MKL threads
    if 'MKL_NUM_THREADS' not in os.environ:
        os.environ['MKL_NUM_THREADS'] = '16'


def evaluate_threads_runtime(max_num_threads=99):
    """ Evaluate how a runtime of matrix multiplication changes with the number of threads """
    runtimes = []
    threads = [1] + [t for t in range(2, max_num_threads, 2)]

    for i, thread in enumerate(threads):
        # Set current number of threads
        torch.set_num_threads(thread)

        # Calculate runtime for matrix multiplication
        runtime = timeit(
            setup="import torch; x = torch.randn(96, 96); y = torch.randn(96, 96)",
            stmt="torch.mm(x, y)",
            number=100,
        )
        # Save and show current results
        runtimes.append(runtime)
        logger.info(f"index={i},\tthread={thread},\truntime={runtime}")

    # Show final results
    index = runtimes.index(min(runtimes))
    logger.info(f"index={index},\tthread={threads[index]},\truntime={runtimes[index]}\tResult!")


def setup_logger(log_name='exp'):
    """ Setup a logger environments for different purposes

    LEVELS = [TRACE, DEBUG, INFO, SUCCESS, WARNING, ERROR, CRITICAL]
    show messages:
        logger.trace("A trace message.")
        logger.debug("A debug message.")
        logger.info("An info message.")
        logger.success("A success message.")
        logger.warning("A warning message.")
        logger.error("An error message.")
        logger.critical("A critical message.")

        colorize=None --> the choice is automatically made based on the sink being a tty or not.
    """
    folder = "loggers"
    Path(folder).mkdir(parents=True, exist_ok=True)
    cur_date_time = datetime.now().strftime("%d.%m.%Y-%H-%M-%S")

    # For terminal - configuration to stderr (Optionally)
    logger.remove(0)    # To remove default version of logger
    default_format = "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    logger.add(sink=sys.stderr, level='INFO', format=default_format, filter=None, colorize=None, serialize=False, backtrace=True, diagnose=True, enqueue=False, context=None, catch=True)

    # For logger file - configuration
    log_path = os.path.join(folder, f"{log_name}_{cur_date_time}.log")
    log_format = "{time:YYYY-MM-DD HH:mm:ss} | <level>{level: <8}</level> | <level>{message}</level>"
    logger.add(sink=log_path, level="TRACE", format=log_format, colorize=None, rotation="10 MB")


def strip_optimizer(filename):
    x = torch.load(filename, map_location=torch.device('cpu'))
    x['model'].half()   # to FP16
    for p in x['model'].parameters():
        p.requires_grad = False
    torch.save(x, filename)


def load_weight(ckpt, model):
    dst = model.state_dict()
    src = torch.load(ckpt, 'cpu')['model'].float().state_dict()

    ckpt = {}
    for k, v in src.items():
        if k in dst and v.shape == dst[k].shape:
            ckpt[k] = v
    model.load_state_dict(state_dict=ckpt, strict=False)
    return model


def weight_decay(model, decay=1e-4):
    """ Param groups weight decay """
    p1, p2 = [], []
    for name, param in model.named_parameters():
        if not param.requires_grad:
            continue

        if len(param.shape) == 1 or name.endswith(".bias"):
            p1.append(param)
        else:
            p2.append(param)

    return [{'params': p1, 'weight_decay': 0.0},
            {'params': p2, 'weight_decay': decay}]


def clip_gradients(model, max_norm=10.0):
    parameters = model.parameters()
    torch.nn.utils.clip_grad_norm_(parameters, max_norm=max_norm)


def set_experiment_results_output(args, root="runs"):
    experiment_path = ""

    if args["training"]["save_dir"] is not None:
        root = args["training"]["save_dir"]

    if os.path.exists(root):
        exps = os.listdir(root)
        exps_index = [int(exp.split("_")[-1]) for exp in exps if exp.startswith("exp_") and exp.split("_")[-1].isdigit()]
        next_index = max(exps_index) + 1 if exps_index else 0
        experiment_path = os.path.join(root, f"exp_{next_index}")
    else:
        experiment_path = os.path.join(root, f"exp_0")

    return experiment_path


def safe_yaml_config_file(args):
    """ Save the training config file in yaml format """
    clean_args = vars(args)
    with open(os.path.join(args.save_dir, "training_config_pipeline.yaml"), 'w') as yaml_file:
        yaml.dump(clean_args, yaml_file, default_flow_style=False)


def set_experiment_logger(args):
    """ Create a logger file in experiment logger """
    cur_date_time = datetime.now().strftime("%d.%m.%Y-%H-%M-%S")
    log_path = os.path.join(args.output_dir, f"{cur_date_time}.log")
    log_format = "{time:YYYY-MM-DD HH:mm:ss} | <level>{level: <8}</level> | <level>{message}</level>"
    logger.add(sink=log_path, level="TRACE", format=log_format, colorize=None, rotation="10 MB")


def save_results_csv(args, training_results, csv_file_name="training_results.csv"):
    save_path = os.path.join(args.output_dir, csv_file_name)

    # Define the CSV file's headers
    headers = ["epoch",
               "train_loss", "val_loss", "val_loss_ema",
               "train_F1score", "val_F1score", "val_F1score_ema",
               "best_F1score", "best_F1score_ema", "lr", "status", "status_ema"]

    # Writing to the CSV file
    with open(save_path, mode='w', newline='') as file:
        writer = csv.DictWriter(file, fieldnames=headers)
        writer.writeheader()
        for result in training_results:
            writer.writerow(result)


def symplify_onnx_model(onnx_path):
    # Load your predefined ONNX model
    model = onnx.load(onnx_path)

    # Convert model
    model_simplified, check = simplify(model)
    assert check, "Simplified ONNX model could not be validated"

    # Save simplified model
    onnx.save(model_simplified, onnx_path)


def check_onnx_model(onnx_path, verbose=False):
    # Load the ONNX model
    model = onnx.load(onnx_path)

    # Check that the model is well formed
    onnx.checker.check_model(model, full_check=True)

    # Print a human readable representation of the graph
    if verbose: print(onnx.helper.printable_graph(model.graph))


def export_onnx_model(args, model, example_input, onnx_path, simplify=True, verify=True):
    """ Save the model in ONNX format """
    export_params = True
    do_constant_folding = True
    input_names = ["input"]
    output_names = ["output"]
    dynamic_axes = None
    verbose = False

    if dynamic_axes:
        dynamic_axes = {
            input_names[0]: {0: 'batch_size'},
            output_names[0]: {0: 'batch_size'}
        }

    # Export the PyTorch model to ONNX
    torch.onnx.export(
        model=model,                                # model being run
        args=example_input,                         # model input (or a tuple for multiple inputs)
        f=onnx_path,                                # where to save the model
        export_params=export_params,                # store the trained parameter weights inside the model
        opset_version=args.opset_version,           # the ONNX version to export the model to
        do_constant_folding=do_constant_folding,    # to execute constant folding for optimization
        input_names=input_names,                    # specify the names of input
        output_names=output_names,                  # specify the names of output
        verbose=verbose,                            # prints a description of the model being exported to stdout
        dynamic_axes=dynamic_axes                   # variable length axes
    )

    if simplify: symplify_onnx_model(onnx_path)
    if verify: check_onnx_model(onnx_path)


def save_result(args, epoch, model, acc, loss, train_acc, train_loss, save=False, EMA=""):
    """ Save loggers """
    type_set, status_type, ema = (EMA, EMA, f"_{EMA}") if EMA == "EMA" else ("val", " "*len("EMA"), "")
    storage = {}
    
    if acc > storage[type_set]["best_acc"]:
        storage[type_set]["status"] = "best-acc"
        storage[type_set]["best_epoch"] = epoch
        storage[type_set]["best_acc"] = acc
        storage[type_set]["best_loss"] = loss
        storage[type_set]["best_train_acc"] = train_acc
        storage[type_set]["best_train_loss"] = train_loss
    elif (acc == storage[type_set]["best_acc"]) and (loss < storage[type_set]["best_loss"]):
        storage[type_set]["status"] = "best-acc-loss"
        storage[type_set]["best_epoch"] = epoch
        storage[type_set]["best_acc"] = acc
        storage[type_set]["best_loss"] = loss
        storage[type_set]["best_train_acc"] = train_acc
        storage[type_set]["best_train_loss"] = train_loss
    else:
        storage[type_set]["status"] = "pass"
    
    # Get log info
    epoch_info = f"epoch: {str(epoch).zfill(3)}"
    acc = f"acc: {round(acc, 8):<11}"
    loss = f"loss: {round(loss, 8):<11}"
    train_acc = f"train_acc: {round(train_acc, 8):<11}"
    train_loss = f"train_loss: {round(train_loss, 8):<11}"
    status = f"status: {status_type} {storage[type_set]['status']}"

    best_epoch = f"epoch: {str(storage[type_set]['best_epoch']).zfill(3)}"
    best_acc = f"acc: {round(storage[type_set]['best_acc'], 8):<11}"
    best_loss = f"loss: {round(storage[type_set]['best_loss'], 8):<11}"
    best_train_acc = f"train_acc: {round(storage[type_set]['best_train_acc'], 8):<11}"
    best_train_loss = f"train_loss: {round(storage[type_set]['best_train_loss'], 8):<11}"
    best_status = f"status: {status_type} Best epoch"

    # Save logs
    logger.debug(f"{epoch_info}  |  {acc}  |  {loss}  |  {train_acc}  |  {train_loss}  |  {status}")
    if args.epochs == epoch:
        if type_set == "val": logger.trace("\n")
        logger.success(f"{best_epoch}  |  {best_acc}  |  {best_loss}  |  {best_train_acc}  |  {best_train_loss}  |  {best_status}")

    if not save:
        return storage
    
    # Save model
    save_path = os.path.join(args.output_dir, "weights")
    os.makedirs(save_path, exist_ok=True)

    if hasattr(model, 'module'):
        model = model.module

    states = {
        "16": {'model': copy.deepcopy(model).half()},
        "32": {'model': copy.deepcopy(model)},
    }

    for key, state in states.items():
        # Save the model on the last epoch
        torch.save(state, f"{save_path}/last_{key}{ema}.pt")

        if "best" in storage[type_set]["status"]:
            if (epoch > 200):
                # Save models on the best epochs
                torch.save(state, f"{save_path}/epoch_{epoch}_{key}{ema}.pt")
            # Save the model on the best epoch
            torch.save(state, f"{save_path}/best_{key}{ema}.pt")

            # Input example to trace the model
            example_input = torch.randn(1, args.input_channels, args.input_size, args.input_size, device=args.device)
            if key == "16":
                example_input = example_input.half()
            
            # For a traced model
            traced_model = torch.jit.trace(state["model"], example_input)
            torch.jit.save(traced_model, f"{save_path}/best_traced_{key}{ema}.pt")

            # For a scripted model
            scripted_model = torch.jit.script(state["model"])
            torch.jit.save(scripted_model, f"{save_path}/best_scripted_{key}{ema}.pt")

            # Save onnx model
            if key == "32":
                export_onnx_model(args, state["model"], example_input, f"{save_path}/best_onnx_{key}{ema}.onnx")
    return storage


class ColoredTqdmFile(object):
    """ Wrapper around a file object that adds color codes to the write method """
    def __init__(self, file, color_code):
        self._file = file
        self._reset_code = '\033[0m'
        self._color_code = {
            "RED": '\033[91m',
            "BLUE": '\033[94m',
            "GREEN": '\033[32m',
            "YELLOW": '\033[33m',
        }[color_code]

    def write(self, s):
        self._file.write(f'{self._color_code}{s}{self._reset_code}')

    def flush(self):
        self._file.flush()


# =============================================================================
def get_custom_learning_rate(args, epoch):
    """
    choices=['step','poly','exponential', 'cosine']
    cosine:         good, but decay lr to slow
    step:           good, you have to manually specify milestones
    exponential:    good, looks like step with auto milestones (better then step?)
    poly:           good, looks like smoothed exponential (better then exponential?)

    step        (args.epochs = 500, args.multistep_gamma = 0.5, args.milestones_step = 20)
    exponential (args.epochs = 500, args.multistep_gamma = 0.9, args.step_size = 3)
    poly        (args.epochs = 500, args.polystep_power = 12)
    """
    cur_lr = args.lr

    if args.warmup_epochs and (epoch <= args.warmup_epochs):
        cur_lr = epoch * args.lr / args.warmup_epochs
        if epoch == 0 and args.warmup_factor is not None:
            cur_lr = max(cur_lr, args.lr * args.warmup_factor)

    elif args.scheduler == 'step':
        num_milestones = 0
        for m in args.milestones:
            num_milestones += (1 if epoch >= m else 0)
        cur_lr = args.lr * (args.multistep_gamma ** num_milestones)

    elif args.scheduler == 'poly':
        epoch_frac = (args.epochs - epoch) / args.epochs
        epoch_frac = max(epoch_frac, 0)
        cur_lr = args.lr * (epoch_frac ** args.polystep_power)

    elif args.scheduler == 'exponential':
        cur_lr = args.lr * (args.multistep_gamma ** (epoch//args.step_size))

    elif args.scheduler == 'cosine':
        if epoch == 0:
            cur_lr = args.lr
        else:
            lr_min = 0
            cur_lr = (args.lr - lr_min) * (1 + math.cos(math.pi * epoch / args.epochs)) / 2.0 + lr_min

    return cur_lr


def plot_custom_LR_scheduler(args_original, show=False):
    """ Visualize LR-scheduler """
    parser = argparse.ArgumentParser()
    parser.add_argument('--output-dir', type=str, default="learning_rates")
    args = parser.parse_args()

    os.makedirs(args.output_dir, exist_ok=True)

    args.epochs = 10
    args.milestones_step = 10
    milestones = list(range(args.milestones_step, args.epochs+1, args.milestones_step))

    args.warmup_epochs = 0
    args.warmup_factor = 1e-3

    args.lr = 0.00001
    args.scheduler = 'poly'             # choices=['step','poly','exponential', 'cosine']
    args.milestones = milestones        # epochs at which learning rate is divided ('step')
    args.multistep_gamma = 0.4          # multi step gamma (default: 0.1) ('step', 'exponential')
    args.step_size = 3                  # step size for exp lr decay ('exponential')
    args.polystep_power = 5             # poly step gamma (default: 1.0) ('poly')

    save_path = os.path.join(args.output_dir, f"lr_scheduler_{args.scheduler}.svg")

    lr_steps = list()
    epochs = list(range(args.epochs))

    for i, epoch in enumerate(epochs):
        cur_lr = get_custom_learning_rate(args, epoch)
        lr_steps.append(cur_lr)
        if show: logger.info(f"{i}: {cur_lr}")

    # Initialize the plot
    plt.figure(figsize=(20, 12))
    plt.title(f'LR scheduler: {args.scheduler}', fontsize=20)
    plt.xlabel('Epoch', fontsize=16)
    plt.ylabel('LR', fontsize=16)

    # Plot the training and validation accuracies
    plt.plot(epochs, lr_steps, label='LR scheduler', marker='o')

    # Show grid, legend, and labels
    plt.grid(True)
    plt.legend()
    plt.savefig(save_path, dpi=600)
    plt.close()
# =============================================================================


def plot_learning_rate_test(epochs=100, lr=0.001):
    """ Select and plot learning rate schedule and optimizer """
    parent_dir = str(Path(__file__).resolve().parent.parent)
    sys.path.append(parent_dir)

    from nets import nn, nn_custom
    model = nn_custom.MobileNet(num_classes=2).cuda()

    idx_o, idx_s = 0, 0
    optimizers = [
        torch.optim.SGD(params=model.parameters(), lr=lr, momentum=0., weight_decay=0),
        torch.optim.SGD(params=weight_decay(model), lr=lr, momentum=0., weight_decay=0),
        torch.optim.Adam(params=weight_decay(model), lr=lr, betas=(0.9, 0.999), eps=1e-8, weight_decay=0),
        torch.optim.RMSprop(params=weight_decay(model), lr=lr, alpha=0.99, eps=1e-8, weight_decay=0, momentum=0),
    ]
    optimizer = optimizers[idx_o]
    schedulers = [
        torch.optim.lr_scheduler.StepLR(optimizer, step_size=30, gamma=0.1),
        torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs, eta_min=0),
        torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=100, T_mult=1, eta_min=0),
        nn.CosineLR(optimizer=optimizer, epochs=epochs, warmup_epochs=5, min_lr=1e-6, max_lr=1e-4),
    ]
    scheduler = schedulers[idx_s]
    plot_lr(epochs=epochs, optimizer=optimizer, scheduler=scheduler)


def plot_lr(epochs, optimizer, scheduler, save_path=""):
    """ Plot learning rate schedule """
    save_path = os.path.join(save_path, "lr_scheduler.svg")

    optimizer = copy.copy(optimizer)
    scheduler = copy.copy(scheduler)

    lr_steps = []
    epochs = list(range(1, epochs+1))

    for epoch in epochs:
        scheduler.step(epoch-1)
        lr_steps.append(optimizer.param_groups[0]['lr'])
    scheduler.step(0)

    # Initialize the plot
    plt.figure(figsize=(20, 12))
    plt.title(f'LR scheduler: {scheduler.__class__.__name__}', fontsize=20)
    plt.xlabel('Epoch', fontsize=16)
    plt.ylabel('LR', fontsize=16)

    # Plot the training and validation accuracies
    plt.plot(epochs, lr_steps, label='LR scheduler', marker='o')

    # Show grid, legend, and labels
    plt.grid(True)
    plt.legend()
    plt.savefig(save_path, dpi=600)
    plt.close()


def plot_actual_learning_rate(args, results):
    save_path = os.path.join(args.output_dir, f"actual_learning_rate.svg")
    
    # Extract epoch numbers, accuracies, and losses for training and validation
    epochs, learning_rates = zip(*results)

    # Initialize the plot
    plt.figure(figsize=(20, 12))
    plt.title('Actual learning rate', fontsize=20)
    plt.xlabel('Epoch', fontsize=16)
    plt.ylabel('LR', fontsize=16)

    # Plot the training and validation accuracies
    plt.plot(epochs, learning_rates, label='LR scheduler', marker='o')

    # Show grid, legend, and labels
    plt.grid(True)
    plt.legend()
    plt.savefig(save_path, dpi=600)
    plt.close()


def plot_performance_results(args, performance_results):
    save_path = os.path.join(args.output_dir, f"performance_results.svg")
    
    # Extract epoch numbers, accuracies, and losses for training and validation
    epochs, train_accuracies, val_accuracies, train_losses, val_losses = zip(*performance_results)

    best_train_accuracy = max(train_accuracies)
    best_val_accuracy = max(val_accuracies)
    stop_epoch = val_accuracies.index(best_val_accuracy)
    train_stop_accuracy = train_accuracies[stop_epoch]
    stop_epoch += 1

    best_train_loss = min(train_losses)
    best_val_loss = min(val_losses)
    stop_epoch_loss = val_losses.index(best_val_loss)
    train_stop_loss = train_losses[stop_epoch_loss]
    stop_epoch_loss += 1

    results = "Epoch={0}/{1}, V-loss={2:.5f}, TM-Acc={3:.3f}, T-Acc={4:.3f}, V-Acc={5:.3f}".format(
        stop_epoch, stop_epoch_loss, best_val_loss, best_train_accuracy, train_stop_accuracy, best_val_accuracy)
    
    # Initialize the plot
    fig, ax1 = plt.subplots(figsize=(20, 12))
    plt.title(f'Results: {results}', fontsize=20)
    plt.xlabel('Epoch', fontsize=16)
    ax1.set_ylabel('Accuracy (%)', fontsize=16)

    # Plot the training and validation accuracies
    ax1.plot(epochs, train_accuracies, label='Train F1Score', marker='o')
    ax1.plot(epochs, val_accuracies, label='Val F1Score', marker='o')
    ax1.tick_params(axis='y')

    ax1.scatter(x=stop_epoch, y=best_val_accuracy, s=50, color='k', zorder=5.5)
    ax1.axhline(y=best_val_accuracy, color='k', linestyle='--', linewidth=2.0)
    ax1.axvline(x=stop_epoch, color='k', linestyle='--', linewidth=2.0)

    # Create a second y-axis for the loss
    ax2 = ax1.twinx()
    ax2.set_ylabel('Loss', fontsize=16, color='r')
    ax2.plot(epochs, train_losses, color='b', linestyle='--', marker='o', label='Train Loss', alpha=0.6)
    ax2.plot(epochs, val_losses, color='r', linestyle='--', marker='o', label='Val Loss')
    ax2.tick_params(axis='y', labelcolor='r')

    ax2.scatter(x=stop_epoch_loss, y=best_val_loss, s=50, color='k', zorder=5.5)
    ax2.axhline(y=best_val_loss, color='k', linestyle='--', linewidth=2.0)
    ax2.axvline(x=stop_epoch_loss, color='r', linestyle='--', linewidth=1.0, alpha=0.6)

    # Show grid, legend, and labels
    ax1.grid(True)
    lines, labels = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines + lines2, labels + labels2, loc='center right')

    plt.savefig(save_path, dpi=600)
    plt.close()


if __name__ == '__main__':
    # setup_logger(log_name="test")
    # plot_learning_rate_test()
    evaluate_threads_runtime()
    # plot_custom_LR_scheduler(None)