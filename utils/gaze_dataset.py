import os

import torch
import skimage.io
import numpy as np
import pandas as pd
import albumentations as A
from albumentations.pytorch import ToTensorV2
from torch.utils.data import Dataset, DataLoader


class CustomGazeDataset(Dataset):
    """
    Dataset for custom normalized gaze data saved as images and a labels CSV.

    Expects a directory structure:
      <data_root>/labels.csv
      <data_root>/pXX/<basename>-face.jpg
      <data_root>/pXX/<basename>-left-eye.jpg
      <data_root>/pXX/<basename>-right-eye.jpg

    CSV columns: face_file_name, left_eye, right_eye, pitch, yaw

    Args:
        data_path: Path to the data directory containing labels.csv and image folders
        transform: Albumentations transform pipeline to apply to images
    """
    def __init__(self, data_path: str, transform=None):
        self.data_path = data_path
        self.transform = transform
        self.df = pd.read_csv(f"{data_path}/labels.csv")

    def __len__(self):
        return len(self.df)

    def __getitem__(self, idx):
        row = self.df.iloc[idx]

        # Derive person_idx from session prefix 'pXX/...'
        session = row.face_file_name.split('/')[0]
        person_idx = int(session[1:])  # 'p00' -> 0
        person_idx = torch.tensor(person_idx, dtype=torch.long)

        # Load images
        face = skimage.io.imread(f"{self.data_path}/{row.face_file_name}")
        left = skimage.io.imread(f"{self.data_path}/{row.left_eye}")
        right = skimage.io.imread(f"{self.data_path}/{row.right_eye}")

        # Apply transforms (optional: flipping or augmentations)
        face_tensor = self.transform(image=face)["image"]
        left_tensor = self.transform(image=left)["image"]
        right_tensor = self.transform(image=right)["image"]

        # Get labels
        pitch = torch.tensor(row.pitch, dtype=torch.float32)
        yaw = torch.tensor(row.yaw, dtype=torch.float32)

        return {
            'person_idx':      person_idx,
            'full_face_image': face_tensor,
            'left_eye_image':  left_tensor,
            'right_eye_image': right_tensor,
            'gaze_pitch':      pitch,
            'gaze_yaw':        yaw
        }


def get_dataloaders(data_root: str, batch_size: int = 16, val_split: float = 0.1, augmentation_config: dict = None):
    """
    Create train/val/test dataloaders by splitting the CSV rows.

    Args:
        data_root: Root directory containing `labels.csv` and image sub-folders
        batch_size: Batch size for all loaders
        val_split: Fraction for validation set
        augmentation_config: Configuration for data augmentation
            {
                'enabled': bool,
                'shift_limit': float,
                'scale_limit': float,
                'rotate_limit': int,
                'brightness_contrast': bool,
                'blur': bool
            }

    Returns:
        tuple: (train_loader, val_loader, test_loader)
    """
    train_data_path = os.path.join(data_root, 'train')
    test_data_path = os.path.join(data_root, 'test')

    # Create val, test transforms
    transform_val = transform_test = A.Compose([
        A.Normalize(),
        ToTensorV2()
    ])

    # Set default augmentation parameters if not provided
    if augmentation_config is None:
        augmentation_config = {
            'enabled': True,
            'shift_limit': 0.2,
            'scale_limit': 0.1,
            'rotate_limit': 10,
            'brightness_contrast': False,
            'blur': False
        }

    # Create train transforms with configurable augmentation
    augmentations = []

    if augmentation_config.get('enabled', True):
        # Add ShiftScaleRotate augmentation
        augmentations.append(
            A.ShiftScaleRotate(
                p=1.0,
                shift_limit=augmentation_config.get('shift_limit', 0.2),
                scale_limit=augmentation_config.get('scale_limit', 0.1),
                rotate_limit=augmentation_config.get('rotate_limit', 10)
            )
        )

        # Add optional brightness/contrast augmentation
        if augmentation_config.get('brightness_contrast', False):
            augmentations.append(
                A.RandomBrightnessContrast(p=0.5)
            )

        # Add optional blur augmentation
        if augmentation_config.get('blur', False):
            augmentations.append(
                A.GaussianBlur(p=0.3)
            )

    # Add normalization and conversion to tensor
    augmentations.extend([
        A.Normalize(),
        ToTensorV2()
    ])

    transform_train = A.Compose(augmentations)

    # Split dataset
    df = pd.read_csv(f"{train_data_path}/labels.csv")

    n = len(df)
    indices = list(range(n))
    np.random.shuffle(indices)
    val_size = int(n * val_split)

    val_idx = indices[:val_size]
    train_idx = indices[val_size:]

    # Create three datasets with different transforms
    train_ds = torch.utils.data.Subset(
        CustomGazeDataset(train_data_path, transform=transform_train), train_idx
    )
    val_ds = torch.utils.data.Subset(
        CustomGazeDataset(train_data_path, transform=transform_val), val_idx
    )
    test_ds = CustomGazeDataset(test_data_path, transform=transform_test)

    # Create dataloaders
    train_loader = DataLoader(train_ds, batch_size=batch_size, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_ds, batch_size=batch_size, shuffle=False, num_workers=4)
    test_loader = DataLoader(test_ds, batch_size=batch_size, shuffle=False, num_workers=4)

    return train_loader, val_loader, test_loader
