import io
from enum import Enum
from typing import List

import torch
import numpy as np
import torchvision
import torch.nn.functional as F
import matplotlib
matplotlib.use('Agg')
from PIL import Image
from matplotlib import pyplot as plt
from pytorch_lightning.loggers import TensorBoardLogger


class PitchYaw(Enum):
    PITCH = 'pitch'
    YAW = 'yaw'


def pitchyaw_to_3d_vector(pitchyaw: torch.Tensor) -> torch.Tensor:
    """
    Convert 2D pitch and yaw angles to 3D unit direction vectors.

    Args:
        pitchyaw: Tensor of shape (N, 2), where [:, 0] = pitch and [:, 1] = yaw (in radians)

    Returns:
        Tensor of shape (N, 3) containing 3D unit direction vectors
    """
    pitch = pitchyaw[:, 0]
    yaw = pitchyaw[:, 1]

    x = -torch.cos(pitch) * torch.sin(yaw)
    y = -torch.sin(pitch)
    z = -torch.cos(pitch) * torch.cos(yaw)

    return torch.stack([x, y, z], dim=1)


def pitchyaw_to_3d_vector_numpy(pitchyaw: np.ndarray) -> np.ndarray:
    """
    Convert 2D pitch and yaw angles to 3D unit direction vectors (NumPy version).

    Args:
        pitchyaw: Array of shape (N, 2), where [:, 0] = pitch and [:, 1] = yaw (in radians)

    Returns:
        Array of shape (N, 3) containing 3D unit direction vectors
    """
    pitch = pitchyaw[:, 0]
    yaw = pitchyaw[:, 1]

    x = -np.cos(pitch) * np.sin(yaw)
    y = -np.sin(pitch)
    z = -np.cos(pitch) * np.cos(yaw)

    return np.stack([x, y, z], axis=1)


def pitchyaw_to_gaze_vector(pitch: np.ndarray, yaw: np.ndarray) -> np.ndarray:
    """
    Convert pitch and yaw angles to a 3D gaze direction vector.

    Args:
        pitch: Pitch angle in radians
        yaw: Yaw angle in radians

    Returns:
        3D gaze direction vector as numpy array [x, y, z]
    """
    x = -np.cos(pitch) * np.sin(yaw)
    y = -np.sin(pitch)
    z = -np.cos(pitch) * np.cos(yaw)
    return np.array([x, y, z])


def angular_loss(labels: torch.Tensor, outputs: torch.Tensor, eps: float = 1e-8) -> torch.Tensor:
    """
    Compute the mean angular loss between predicted and ground truth gaze directions.

    This function converts pitch-yaw pairs to 3D vectors and calculates the angular
    difference between them using the cosine similarity with numerical stability improvements.

    Args:
        labels: Ground truth gaze angles (pitch, yaw) of shape (N, 2)
        outputs: Predicted gaze angles (pitch, yaw) of shape (N, 2)
        eps: Small epsilon value for numerical stability

    Returns:
        Mean angular error in radians as a scalar tensor
    """
    # Clamp input angles to reasonable ranges to prevent extreme values
    labels = torch.clamp(labels, -torch.pi, torch.pi)
    outputs = torch.clamp(outputs, -torch.pi, torch.pi)

    # Convert to 3D vectors
    labels_vec = pitchyaw_to_3d_vector(labels)
    outputs_vec = pitchyaw_to_3d_vector(outputs)

    # Normalize vectors to ensure they are unit vectors
    labels_vec = F.normalize(labels_vec, p=2, dim=1, eps=eps)
    outputs_vec = F.normalize(outputs_vec, p=2, dim=1, eps=eps)

    # Compute cosine similarity with additional safety
    cosine_sim = F.cosine_similarity(outputs_vec, labels_vec, dim=1, eps=eps)

    # Clamp to valid range for arccos with tighter bounds for numerical stability
    cosine_sim = torch.clamp(cosine_sim, -1.0 + eps, 1.0 - eps)

    # Compute angular error
    angular_error_rad = torch.arccos(cosine_sim)

    return angular_error_rad.mean()


def calc_angle_error(labels: torch.Tensor, outputs: torch.Tensor) -> torch.Tensor:
    """
    Calculate the angular error between predicted and ground truth gaze directions in degrees.

    Args:
        labels: Ground truth gaze angles (pitch, yaw) of shape (N, 2)
        outputs: Predicted gaze angles (pitch, yaw) of shape (N, 2)

    Returns:
        Mean angular error in degrees as a scalar tensor
    """
    labels = pitchyaw_to_3d_vector(labels)
    labels_norm = labels / torch.linalg.norm(labels, axis=1).reshape((-1, 1))

    outputs = pitchyaw_to_3d_vector(outputs)
    outputs_norm = outputs / torch.linalg.norm(outputs, axis=1).reshape((-1, 1))

    angles = F.cosine_similarity(outputs_norm, labels_norm, dim=1)
    angles = torch.clip(angles, -1.0, 1.0)  # clamp values to [-1.0, 1.0] to avoid instability

    rad = torch.arccos(angles)
    return torch.rad2deg(rad).mean()


def plot_prediction_vs_ground_truth(labels: torch.Tensor, outputs: torch.Tensor, axis: PitchYaw):
    """
    Create a scatter plot comparing predictions with ground truth values.

    Args:
        labels: Ground truth gaze angles (pitch, yaw) of shape (N, 2)
        outputs: Predicted gaze angles (pitch, yaw) of shape (N, 2)
        axis: Whether to plot pitch or yaw values

    Returns:
        Matplotlib figure object containing the scatter plot
    """

    labels = torch.rad2deg(labels)
    outputs = torch.rad2deg(outputs)

    if axis == PitchYaw.PITCH:
        plt.scatter(labels[:, :1].cpu().detach().numpy().reshape(-1),
                    outputs[:, :1].cpu().detach().numpy().reshape(-1))
    else:
        plt.scatter(labels[:, 1:].cpu().detach().numpy().reshape(-1),
                    outputs[:, 1:].cpu().detach().numpy().reshape(-1))
    plt.plot([-30, 30], [-30, 30], color='#ff7f0e')
    plt.xlabel('Ground Truth (degrees)')
    plt.ylabel('Prediction (degrees)')
    plt.title(f'{axis.value.capitalize()} Prediction vs Ground Truth')
    if axis == PitchYaw.PITCH:
        plt.xlim((-30, 5))
        plt.ylim((-30, 5))
    else:
        plt.xlim((-30, 30))
        plt.ylim((-30, 30))

    return plt.gcf()


def plot_to_image(fig) -> torch.Tensor:
    """
    Converts the matplotlib plot specified by 'figure' to a PNG image and
    returns it. The supplied figure is closed and inaccessible after this call.

    :param fig: matplotlib figure
    :return: plot for torchvision
    """

    # Save the plot to a PNG in memory.
    buf = io.BytesIO()
    plt.savefig(buf, format="png")
    plt.close(fig)
    buf.seek(0)

    image = Image.open(buf).convert("RGB")
    image = torchvision.transforms.ToTensor()(image)
    return image


def log_figure(loggers: List, tag: str, figure, global_step: int) -> None:
    """
    Log figure as image. Only works for `TensorBoardLogger`.

    :param loggers:
    :param tag:
    :param figure:
    :param global_step:
    :return:
    """
    if isinstance(loggers, list):
        for logger in loggers:
            if isinstance(logger, TensorBoardLogger):
                logger.experiment.add_image(tag, plot_to_image(figure), global_step, dataformats="CHW")
    elif isinstance(loggers, TensorBoardLogger):
        loggers.experiment.add_image(tag, plot_to_image(figure), global_step, dataformats="CHW")
