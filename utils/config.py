"""
Configuration utilities for the Gaze Tracking Pipeline.
"""

import os
import yaml
from typing import Dict, Any
from argparse import ArgumentParser, Namespace


def show_config(config: Dict[str, Any]) -> None:
    """
    Print configuration summary.

    Args:
        config: Configuration dictionary
    """
    # Print configuration summary
    print("\nRunning with configuration:")
    print(f"  Dataset path: {config['training']['dataset_path']}")

    # Training parameters
    print("\nTraining parameters:")
    print(f"  Training enabled: {config['training']['train']}")
    print(f"  Testing enabled: {config['training']['test']}")
    print(f"  Epochs: {config['training']['epochs']}")
    print(f"  Batch size: {config['training']['batch_size']}")
    print(f"  Learning rate: {config['training']['learning_rate']}")
    print(f"  Weight decay: {config['training']['weight_decay']}")
    print(f"  Checkpoint: {config['training']['checkpoint_path'] or 'None'}")

    # Loss weights
    loss_weights = config['training'].get('loss_weights', {'angular': 1.0, 'l1': 0.1})
    print(f"  Loss weights: angular={loss_weights.get('angular', 1.0)}, l1={loss_weights.get('l1', 0.1)}")

    # Augmentation parameters
    aug_config = config.get('augmentation', {})
    print("\nAugmentation parameters:")
    print(f"  Enabled: {aug_config.get('enabled', True)}")
    if aug_config.get('enabled', True):
        print(f"  Shift limit: {aug_config.get('shift_limit', 0.2)}")
        print(f"  Scale limit: {aug_config.get('scale_limit', 0.1)}")
        print(f"  Rotate limit: {aug_config.get('rotate_limit', 10)}")
        print(f"  Brightness/contrast: {aug_config.get('brightness_contrast', False)}")
        print(f"  Blur: {aug_config.get('blur', False)}")
    print()


def save_config(config: Dict[str, Any], config_path: str) -> None:
    """
    Save configuration to a YAML file.

    Args:
        config: Configuration dictionary
        config_path: Path to save the YAML configuration file
    """
    os.makedirs(os.path.dirname(config_path), exist_ok=True)
    with open(config_path, 'w') as f:
        yaml.dump(config, f, default_flow_style=False)


def load_config(config_path: str) -> Dict[str, Any]:
    """
    Load configuration from a YAML file.

    Args:
        config_path: Path to the YAML configuration file

    Returns:
        Dictionary containing the configuration
    """
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


def update_config_with_args(config: Dict[str, Any], args: Namespace) -> Dict[str, Any]:
    """
    Update configuration with command-line arguments.
    Command-line arguments take precedence over config file values.

    Args:
        config: Configuration dictionary
        args: Command-line arguments

    Returns:
        Updated configuration dictionary
    """
    # Convert args to dictionary
    args_dict = vars(args)

    # Update training parameters
    if 'train' in args_dict and args_dict['train']:
        config['training']['train'] = args_dict['train']
    if 'test' in args_dict and args_dict['test']:
        config['training']['test'] = args_dict['test']
    if 'epochs' in args_dict and args_dict['epochs'] is not None:
        config['training']['epochs'] = args_dict['epochs']
    if 'batch_size' in args_dict and args_dict['batch_size'] is not None:
        config['training']['batch_size'] = args_dict['batch_size']
    if 'val_split' in args_dict and args_dict['val_split'] is not None:
        config['training']['val_split'] = args_dict['val_split']
    if 'learning_rate' in args_dict and args_dict['learning_rate'] is not None:
        config['training']['learning_rate'] = args_dict['learning_rate']
    if 'weight_decay' in args_dict and args_dict['weight_decay'] is not None:
        config['training']['weight_decay'] = args_dict['weight_decay']
    if 'gpus' in args_dict and args_dict['gpus'] is not None:
        config['training']['gpus'] = args_dict['gpus']
    if 'print_interval' in args_dict and args_dict['print_interval'] is not None:
        config['training']['print_interval'] = args_dict['print_interval']
    if 'checkpoint_path' in args_dict and args_dict['checkpoint_path'] is not None:
        config['training']['checkpoint_path'] = args_dict['checkpoint_path']

    # Update data paths
    if 'dataset_path' in args_dict and args_dict['dataset_path'] is not None:
        config['training']['dataset_path'] = args_dict['dataset_path']

    return config


def get_config_args() -> ArgumentParser:
    """
    Create an argument parser for configuration.

    Returns:
        ArgumentParser object with config-related arguments
    """
    parser = ArgumentParser()
    parser.add_argument('--config', type=str, default='configs/config.yaml',
                        help='Path to the configuration file')
    parser.add_argument('--train', action="store_true", help='Train the model')
    parser.add_argument('--test', action="store_true", help='Test the model')
    parser.add_argument('--epochs', type=int, help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, help='Batch size')
    parser.add_argument('--val_split', type=float, help='Validation split ratio')
    parser.add_argument('--learning_rate', type=float, help='Learning rate')
    parser.add_argument('--weight_decay', type=float, help='L2 regularization coefficient')
    parser.add_argument('--gpus', type=int, help='Number of GPUs to use')
    parser.add_argument('--print_interval', type=int, help='Interval for printing metrics')
    parser.add_argument('--dataset_path', type=str, help='Path to normalized data')
    parser.add_argument('--checkpoint_path', type=str, help='Path to pretrained model')
    args = parser.parse_args()

    return args

def get_config() -> Dict[str, Any]:
    """
    Get configuration from file and command-line arguments.

    Returns:
        Configuration dictionary
    """
    # Parse command-line arguments
    args = get_config_args()

    # Load config from file
    config = load_config(args.config)

    # Update with command-line arguments
    config = update_config_with_args(config, args)

    # Print configuration summary
    show_config(config)

    return config
