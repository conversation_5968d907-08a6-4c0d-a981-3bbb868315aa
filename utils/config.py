"""
Configuration utilities for the Gaze Tracking Pipeline.
"""
import os
from typing import Dict, Any
from argparse import ArgumentParser, Namespace

import yaml
from icecream import ic

from utils import util2


def show_config(config: Dict[str, Any]) -> None:
    """
    Print configuration summary.

    Args:
        config: Configuration dictionary
    """
    print("="*80)

    if config['training']['train']:
        ic(config['training'])
    elif config['training']['test']:
        ic(config['training']['checkpoint_path'])
    else:
        print("No training or testing enabled. Please set --train or --test flag.")
        
    print("="*80)


def save_config(config: Dict[str, Any], config_name: str= "training_config_pipeline.yaml") -> None:
    """
    Save configuration to a YAML file.

    Args:
        config: Configuration dictionary
        config_name: Name of the YAML configuration file
    """
    config_path = config["training"]["save_dir"]
    os.makedirs(config_path, exist_ok=True)
    
    with open(os.path.join(config_path, config_name), 'w') as yaml_file:
        yaml.dump(config, yaml_file, default_flow_style=False)

        
def load_config(config_path: str) -> Dict[str, Any]:
    """
    Load configuration from a YAML file.

    Args:
        config_path: Path to the YAML configuration file

    Returns:
        Dictionary containing the configuration
    """
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


def update_config_with_args(config: Dict[str, Any], args: Namespace) -> Dict[str, Any]:
    """
    Update configuration with command-line arguments.
    Command-line arguments take precedence over config file values.

    Args:
        config: Configuration dictionary
        args: Command-line arguments

    Returns:
        Updated configuration dictionary
    """
    # Convert args to dictionary
    args_dict = vars(args)

    # Update training parameters
    if 'train' in args_dict and args_dict['train']:
        config['training']['train'] = args_dict['train']
    if 'test' in args_dict and args_dict['test']:
        config['training']['test'] = args_dict['test']
    if 'epochs' in args_dict and args_dict['epochs'] is not None:
        config['training']['epochs'] = args_dict['epochs']
    if 'batch_size' in args_dict and args_dict['batch_size'] is not None:
        config['training']['batch_size'] = args_dict['batch_size']
    if 'val_split' in args_dict and args_dict['val_split'] is not None:
        config['training']['val_split'] = args_dict['val_split']
    if 'learning_rate' in args_dict and args_dict['learning_rate'] is not None:
        config['training']['learning_rate'] = args_dict['learning_rate']
    if 'weight_decay' in args_dict and args_dict['weight_decay'] is not None:
        config['training']['weight_decay'] = args_dict['weight_decay']
    if 'gpus' in args_dict and args_dict['gpus'] is not None:
        config['training']['gpus'] = args_dict['gpus']
    if 'print_interval' in args_dict and args_dict['print_interval'] is not None:
        config['training']['print_interval'] = args_dict['print_interval']
    if 'checkpoint_path' in args_dict and args_dict['checkpoint_path'] is not None:
        config['training']['checkpoint_path'] = args_dict['checkpoint_path']

    # Update data paths
    if 'dataset_path' in args_dict and args_dict['dataset_path'] is not None:
        config['training']['dataset_path'] = args_dict['dataset_path']
    
    # Set default dataset path if not provided
    config["training"]["save_dir"] = util2.set_experiment_results_output(config)

    return config


def create_config_args() -> ArgumentParser:
    """
    Create an argument parser for configuration.

    Returns:
        ArgumentParser object with config-related arguments
    """
    parser = ArgumentParser()
    parser.add_argument('--config', type=str, default='configs/config.yaml',
                        help='Path to the configuration file')
    parser.add_argument('--train', action="store_true", help='Train the model')
    parser.add_argument('--test', action="store_true", help='Test the model')
    parser.add_argument('--epochs', type=int, help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, help='Batch size')
    parser.add_argument('--val_split', type=float, help='Validation split ratio')
    parser.add_argument('--learning_rate', type=float, help='Learning rate')
    parser.add_argument('--weight_decay', type=float, help='L2 regularization coefficient')
    parser.add_argument('--gpus', type=int, help='Number of GPUs to use')
    parser.add_argument('--print_interval', type=int, help='Interval for printing metrics')
    parser.add_argument('--dataset_path', type=str, help='Path to normalized data')
    parser.add_argument('--checkpoint_path', type=str, help='Path to pretrained model')
    args = parser.parse_args()

    return args


def get_config() -> Dict[str, Any]:
    """
    Get configuration from file and command-line arguments.

    Returns:
        Configuration dictionary
    """
    # Parse command-line arguments
    args = create_config_args()

    # Load config from file
    config = load_config(args.config)

    # Update with command-line arguments
    config = update_config_with_args(config, args)

    # Print configuration summary
    show_config(config)
    
    # Save updated configuration
    save_config(config)

    return config
