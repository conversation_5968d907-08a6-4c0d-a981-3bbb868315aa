"""
Configuration utilities for the Gaze Tracking Pipeline.
"""

import os
import yaml
from typing import Dict, Any, Optional
from argparse import ArgumentParser, Namespace


def show_config(config: Dict[str, Any]) -> None:
    """
    Print configuration summary.
    
    Args:
        config: Configuration dictionary
    """
    # Print configuration summary
    print("\nRunning with configuration:")
    print(f"  Dataset path: {config['training']['dataset_path']}")

    # Training parameters
    print("\nTraining parameters:")
    print(f"  Training enabled: {config['training']['train']}")
    print(f"  Testing enabled: {config['training']['test']}")
    print(f"  Epochs: {config['training']['epochs']}")
    print(f"  Batch size: {config['training']['batch_size']}")
    print(f"  Learning rate: {config['training']['learning_rate']}")
    print(f"  Weight decay: {config['training']['weight_decay']}")
    print(f"  Checkpoint: {config['training']['checkpoint_path'] or 'None'}")

    # Loss weights
    loss_weights = config['training'].get('loss_weights', {'angular': 1.0, 'l1': 0.1})
    print(f"  Loss weights: angular={loss_weights.get('angular', 1.0)}, l1={loss_weights.get('l1', 0.1)}")

    # Augmentation parameters
    aug_config = config.get('augmentation', {})
    print("\nAugmentation parameters:")
    print(f"  Enabled: {aug_config.get('enabled', True)}")
    if aug_config.get('enabled', True):
        print(f"  Shift limit: {aug_config.get('shift_limit', 0.2)}")
        print(f"  Scale limit: {aug_config.get('scale_limit', 0.1)}")
        print(f"  Rotate limit: {aug_config.get('rotate_limit', 10)}")
        print(f"  Brightness/contrast: {aug_config.get('brightness_contrast', False)}")
        print(f"  Blur: {aug_config.get('blur', False)}")
    print()


def save_config(config: Dict[str, Any], config_path: str) -> None:
    """
    Save configuration to a YAML file.
    
    Args:
        config: Configuration dictionary
        config_path: Path to save the YAML configuration file
    """
    os.makedirs(os.path.dirname(config_path), exist_ok=True)
    with open(config_path, 'w') as f:
        yaml.dump(config, f, default_flow_style=False)


def load_config(config_path: str) -> Dict[str, Any]:
    """
    Load configuration from a YAML file.
    
    Args:
        config_path: Path to the YAML configuration file
        
    Returns:
        Dictionary containing the configuration
    """
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


def update_config_with_args(config: Dict[str, Any], args: Namespace) -> Dict[str, Any]:
    """
    Update configuration with command-line arguments.
    Command-line arguments take precedence over config file values.
    
    Args:
        config: Configuration dictionary
        args: Command-line arguments
        
    Returns:
        Updated configuration dictionary
    """
    # Convert args to dictionary
    args_dict = vars(args)
    
    # Update training parameters
    if 'train' in args_dict and args_dict['train']:
        config['training']['train'] = args_dict['train']
    if 'test' in args_dict and args_dict['test']:
        config['training']['test'] = args_dict['test']
    if 'epochs' in args_dict and args_dict['epochs'] is not None:
        config['training']['epochs'] = args_dict['epochs']
    if 'batch_size' in args_dict and args_dict['batch_size'] is not None:
        config['training']['batch_size'] = args_dict['batch_size']
    if 'val_split' in args_dict and args_dict['val_split'] is not None:
        config['training']['val_split'] = args_dict['val_split']
    if 'learning_rate' in args_dict and args_dict['learning_rate'] is not None:
        config['training']['learning_rate'] = args_dict['learning_rate']
    if 'weight_decay' in args_dict and args_dict['weight_decay'] is not None:
        config['training']['weight_decay'] = args_dict['weight_decay']
    if 'gpus' in args_dict and args_dict['gpus'] is not None:
        config['training']['gpus'] = args_dict['gpus']
    if 'print_interval' in args_dict and args_dict['print_interval'] is not None:
        config['training']['print_interval'] = args_dict['print_interval']
    if 'checkpoint_path' in args_dict and args_dict['checkpoint_path'] is not None:
        config['training']['checkpoint_path'] = args_dict['checkpoint_path']
    
    # Update data paths
    if 'dataset_path' in args_dict and args_dict['dataset_path'] is not None:
        config['training']['dataset_path'] = args_dict['dataset_path']
    
    return config


def get_config_args() -> ArgumentParser:
    """
    Create an argument parser for configuration.
    
    Returns:
        ArgumentParser object with config-related arguments
    """
    parser = ArgumentParser()
    parser.add_argument('--config', type=str, default='configs/config.yaml',
                        help='Path to the configuration file')
    parser.add_argument('--train', action="store_true", help='Train the model')
    parser.add_argument('--test', action="store_true", help='Test the model')
    parser.add_argument('--epochs', type=int, help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, help='Batch size')
    parser.add_argument('--val_split', type=float, help='Validation split ratio')
    parser.add_argument('--learning_rate', type=float, help='Learning rate')
    parser.add_argument('--weight_decay', type=float, help='L2 regularization coefficient')
    parser.add_argument('--gpus', type=int, help='Number of GPUs to use')
    parser.add_argument('--print_interval', type=int, help='Interval for printing metrics')
    parser.add_argument('--dataset_path', type=str, help='Path to normalized data')
    parser.add_argument('--checkpoint_path', type=str, help='Path to pretrained model')
    args = parser.parse_args()

    return args


# def get_parser_args():
#     """
#     Command-line interface for the gaze dataset preprocessing tool.

#     Example usage:
#     ```
#     python scripts/data_preprocessing/gaze_dataset_preprocessing.py \
#         --raw_base=datasets/DeltaX/dataset_raw/ \
#         --normalized_base=datasets/DeltaX/dataset_normalized/ \
#         --intrinsics_path=configs/depth_camera_calibration.yaml
#     ```
#     """
#     # Set up command-line argument parser
#     parser = ArgumentParser()
#     parser.add_argument('--raw_base',
#                         type=str,
#                         default='datasets/DeltaX/dataset_raw/',
#                         help='Raw dataset path')
#     parser.add_argument('--normalized_base',
#                         type=str,
#                         default='datasets/DeltaX/dataset_normalized/',
#                         help='Normolized dataset path')
#     parser.add_argument('--intrinsics_path',
#                         type=str,
#                         default='configs/depth_camera_calibration.yaml',
#                         help='Camera calibration YAML file path')
#     args = parser.parse_args()
#     return args


# def parse_args():
#     """Parse command line arguments."""
#     parser = argparse.ArgumentParser(
#         description="Analyze the distribution of gaze data in raw and normalized formats"
#     )
    
#     parser.add_argument(
#         "--raw_base",
#         default="datasets/DeltaX/dataset_raw/train",
#         type=str, 
#     )
#     parser.add_argument(
#         "--raw_csv", 
#         type=str, 
#         # required=True,
#         default="datasets/DeltaX/dataset_raw/train/data.csv",
#         help="Path to raw data CSV file"
#     )
#     parser.add_argument(
#         "--norm_csv", 
#         type=str, 
#         # required=True,
#         default="datasets/DeltaX/dataset_normalized/train/labels.csv",
#         help="Path to normalized data CSV file"
#     )
#     parser.add_argument(
#         "--intrinsics_path",
#         default="configs/depth_camera_calibration.yaml",
#         type=str, 
#     )
#     parser.add_argument(
#         "--output_dir", 
#         type=str, 
#         default="outputs/distribution_analysis_results_2",
#         help="Directory to save analysis results"
#     )
#     parser.add_argument(
#         "--limit", 
#         type=int, 
#         default=0,
#         help="Limit the number of samples to process (0 = all)"
#     )
    
#     return parser.parse_args()


# def parse_args():
#     """Parse command line arguments for gaze visualization."""
#     parser = argparse.ArgumentParser(description='Visualize gaze direction with ground truth and predictions')

#     # Required arguments
#     parser.add_argument('--csv_path', type=str, required=False,
#                         default='datasets/DeltaX/dataset_normalized/test/labels.csv',
#                         help='Path to CSV file with labels')
#     parser.add_argument('--base_path', type=str, required=False, default='datasets/DeltaX/dataset_normalized/test',
#                         help='Base path for images')

#     # Optional arguments
#     parser.add_argument('--output_path', type=str, default='outputs/gaze_visualization_scratch/',
#                         help='Path to save visualization results (default: outputs/gaze_visualization/)')
#     parser.add_argument('--model_ckpt_path', type=str, default="runs/version_31/checkpoints/best.ckpt",
#                         help='Path to model checkpoint (if not provided, only ground truth will be shown)')
#     parser.add_argument('--arrow_length', type=int, default=50,
#                         help='Length of the gaze arrow in pixels (default: 50)')
#     parser.add_argument('--arrow_thickness', type=int, default=2,
#                         help='Thickness of the gaze arrow (default: 2)')
#     parser.add_argument('--limit', type=int, default=None,
#                         help='Limit the number of samples to process (default: process all)')
#     args = parser.parse_args()
    
#     if args.csv_path is None:
#         args.csv_path = f"{args.base_path}/labels.csv"
        
#     return parser.parse_args()


def get_config() -> Dict[str, Any]:
    """
    Get configuration from file and command-line arguments.
    
    Returns:
        Configuration dictionary
    """
    # Parse command-line arguments
    args = get_config_args()
    
    # Load config from file
    config = load_config(args.config)
    
    # Update with command-line arguments
    config = update_config_with_args(config, args)
    
    # Print configuration summary
    show_config(config)

    return config
